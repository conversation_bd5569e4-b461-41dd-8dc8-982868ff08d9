{"name": "dere-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^4.4.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@rollup/plugin-terser": "^0.4.4", "@svgr/plugin-svgo": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "emailjs-com": "^3.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-icons": "^5.4.0", "react-router-dom": "^7.1.1", "recharts": "^2.15.3", "svgo": "^3.3.2", "tailwind-scrollbar": "^3.1.0", "url": "^0.11.4", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "react-scripts": "^5.0.1", "tailwindcss": "^3.4.17"}}