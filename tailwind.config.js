
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx,html,css}",
  ],
  theme: {
    extend: {
      backgroundImage: {
        'black-to-white-top': 'linear-gradient(to bottom, black 10%, rgba(0, 0, 0, 0.5) 50%, white 90%)',
      },
    },
    screens: {
      sm: "480px",
      md: "897px",
      lg: "976px",
      xl: "1640px",
    },
    fontFamily: {
      sans: ['Inter', 'system-ui', '-apple-system-BlinkMacOS-Font', 'sans-serif'],
      serif: ['Saira', 'serif'],
      arimo: ['Arimo', 'sans-serif'],
    },
  },
  plugins: [
    require('tailwind-scrollbar'),
  ],
}
