

import React from 'react';

const Spinner = () => {
  return (
    <div className="flex flex-col justify-center items-center h-[60vh]">
      <div className="relative flex justify-center items-center h-12 w-12 animate-spin">
        {[...Array(4)].map((_, i) => (
          <div
            key={i}
            className={`absolute w-4 h-4 rounded-full ${
              i === 2 ? 'bg-gray-800' : 'bg-orange-600'
            }`}
            style={{
              transform: i === 0 ? 'translate(-12px, -12px)' :
                     i === 1 ? 'translate(12px, -12px)' :
                     i === 2 ? 'translate(-12px, 12px)' :
                     'translate(12px, 12px)',
            }}
          ></div>
        ))}
      </div>
      <p className="mt-4 text-gray-600 font-serif">Please wait while we load the content...</p>
    </div>
  );
};

export default Spinner;