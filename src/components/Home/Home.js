import React, { useEffect, useState } from 'react';
import Navbar from '../navbar/NavBar';
import Footer from '../footer/Footer';
import TruncatedText from './truncatedtext /TruncatedText';
import { useLocation } from 'react-router-dom';
import Contacts from './Contacts/Contacts';
import { Helmet } from 'react-helmet';

const Home = () => {
  const data = [
    {
      imgSrc: "/assets/carsicon.png",
      heading: "Made for Kenya",
      text: "Built specifically for Kenyan car owners, leasing companies, and taxi operators with local requirements in mind.",
    },
    {
      imgSrc: "/assets/drivericon.png",
      heading: "Reliable Hiring",
      text: "Our flagged driver system helps you avoid repeat fraud or driver unreliability through community-generated performance data.",
    },
    {
      imgSrc: "/assets/Pay.png",
      heading: "Smart Analytics",
      text: "Insights that help you track profitability and reduce losses across your fleet or individual vehicles.",
    },
    {
      imgSrc: "/assets/timevectoricon.png",
      heading: "Saves Time",
      text: "No paperwork, no guesswork – just clear tools to stay on top of your transport business operations.",
    },
  ];

  const location = useLocation();
  const [activeSection, setActiveSection] = useState('home');

  useEffect(() => {
    if (location.hash) {
      const sectionId = location.hash.substring(1);
      const section = document.getElementById(sectionId);
      if (section) {
        section.scrollIntoView({ behavior: 'smooth' });
        setActiveSection(sectionId.replace('-section', '') || 'home');
      }
    }

    const handleScroll = () => {
      const sections = [
        { id: 'about-us-section', name: 'about' },
        { id: 'services-section', name: 'services' },
        { id: 'pricing-section', name: 'pricing' },
        { id: 'contacts-section', name: 'contacts' },
      ];

      for (const section of sections) {
        const element = document.getElementById(section.id);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top >= 0 && rect.top < window.innerHeight / 2) {
            setActiveSection(section.name);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [location]);

  const getHelmetContent = () => {
    switch (activeSection) {
      case 'about':
        return {
          title: 'kadereconnect-How It Works | Simplify Vehicle Hiring & Records - KadereConnect',
          description: 'Learn how KadereConnect simplifies driver hiring, vehicle compliance, and profitability reporting. Ideal for Kenyan fleets.',
          ogTitle: 'How KadereConnect Works',
          ogDescription: 'Simplify driver hiring and vehicle management with KadereConnect, built for Kenyan fleets.',
          ogImage: 'https://kadereconnect.co.ke/assets/og-image-about.jpg',
        };
      case 'services':
        return {
          title: 'kadereconnect-Features | Manage Drivers, Hire Smart, Track Vehicles - KadereConnect',
          description: 'Discover KadereConnect features built for leasing companies and car owners in Kenya. Seamless hiring, tracking, and reporting.',
          ogTitle: 'KadereConnect Features',
          ogDescription: 'Manage drivers, track vehicles, and hire smart with KadereConnect’s tools for Kenyan car owners.',
          ogImage: 'https://kadereconnect.co.ke/assets/og-image-services.jpg',
        };
      case 'pricing':
        return {
          title: 'kadereconnect-Pricing | Simple SaaS Plans for Kenyan Fleet Operators - KadereConnect',
          description: 'Affordable monthly plans for vehicle record keeping, job posting, and lease management in Kenya. Start with a free trial.',
          ogTitle: 'KadereConnect Pricing Plans',
          ogDescription: 'Affordable SaaS plans for Kenyan fleet operators with a free trial.',
          ogImage: 'https://kadereconnect.co.ke/assets/og-image-pricing.jpg',
        };
      case 'contacts':
        return {
          title: 'kadereconnect-Contact | Reach Out to KadereConnect Support Team',
          description: 'Get in touch with KadereConnect for support, demos, or feedback. We’re here to help Kenya’s fleet operators succeed.',
          ogTitle: 'Contact KadereConnect Support',
          ogDescription: 'Reach out for support, demos, or feedback. KadereConnect helps Kenyan fleet operators succeed.',
          ogImage: 'https://kadereconnect.co.ke/assets/og-image-contact.jpg',
        };
      default:
        return {
          title: 'Kadereconnect',
          description: 'KadereConnect is Kenya’s all-in-one vehicle and driver management platform. Hire drivers, manage vehicle records, and more.',
          ogTitle: 'KadereConnect - Vehicle & Driver Management',
          ogDescription: 'Hire trusted drivers and manage vehicles with KadereConnect, Kenya’s all-in-one platform.',
          ogImage: 'https://kadereconnect.co.ke/assets/og-image-home.jpg',
        };
    }
  };

  const { title, description, ogTitle, ogDescription, ogImage } = getHelmetContent();

  return (
    <div className="flex flex-col w-full p-0 m-0 overflow-hidden">
      <Helmet>
        <title>{title}</title>
        <meta name="description" content={description} />
        <link rel="canonical" href={`https://kadereconnect.co.ke${location.pathname}${location.hash}`} />
        <meta property="og:title" content={ogTitle} />
        <meta property="og:description" content={ogDescription} />
        <meta property="og:image" content={ogImage} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={`https://kadereconnect.co.ke${location.pathname}${location.hash}`} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={ogTitle} />
        <meta name="twitter:description" content={ogDescription} />
        <meta name="twitter:image" content={ogImage} />
        <script type="application/ld+json">{`
          {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "KadereConnect",
            "url": "https://kadereconnect.co.ke/",
            "contactPoint": {
              "@type": "ContactPoint",
              "contactType": "Customer Support",
              "url": "https://kadereconnect.co.ke/#contacts-section",
              "email": "<EMAIL>"
            },
            "sameAs": [
              "https://www.facebook.com/kadereconnect",
              "https://www.twitter.com/kadereconnect"
            ]
          }
        `}</script>
      </Helmet>
      {/* navbar */}
      <Navbar />

      {/* First section */}
      <div className="md:h-[85vh] h-screen flex w-full relative">
        <div className="hidden md:block absolute z-20 bottom-0 left-0 w-[70%] h-auto"> 
          <img src="/assets/car12.png" alt="Car" className="w-[100%] xl:w-[95%]" />
        </div>
        <div className="md:hidden absolute z-20 bottom-0 left-0 w-[98%] h-auto">
          <img src="/assets/car132.png" alt="Car" className="w-[95%] xl:w-[95%] mt-10" />
        </div>
        <div className="w-[34%] bg-orange-700 flex flex-col items-start justify-start relative">
          <img src="/assets/track 1.png" alt="Track" className="hidden md:block md:h-[52vh] mt-4 transform translate-x-[10%] w-[100%]" />
        </div>
        <div className="w-[66%] flex flex-col justify-between bg-white mb-10">
          <div className="flex flex-col items-center md:items-start xl:mt-16 mt-6 md:ml-20 xl:ml-32 md:w-[90%] relative">
            <h1 className="md:text-3xl md:items-start justify-start md:text-left text-xl font-bold mb-4 font-serif xl:text-5xl md:px-0 px-6 tracking-wider">
              KadereConnect – All-In-One <br /> Vehicle & Driver Management <br /> for Kenya
            </h1>
            <span className='bg-orange-700 h-[1vh] xl:w-24 w-[80%] md:w-12'></span>
            <p className="md:text-lg text-sm xl:text-3xl text-gray-500 mt-4 md:px-0 px-6 mb-8 font-serif">
              Manage your cars. Hire trusted drivers. Run your business smarter.
            </p>
            <div className="flex justify-center w-full md:px-0 px-6 md:hidden">
              <a href='/jobs' className='z-30'>
                <button className="md:text-xl xl:text-2xl text-xs bg-black text-white px-6 py-3 font-serif font-bold">
                  Get Started
                </button>
              </a>
            </div>
          </div>
          <a href="/jobs" className="z-30 md:block hidden md:self-end">
            <button
              className="md:text-xl xl:text-2xl text-xs md:items-end items-center md:absolute md:bottom-8 md:right-20 bg-black text-white p-3 md:px-5 font-serif font-bold"
              alt="View Opportunities"
            >
              Get Started
            </button>
          </a>
        </div>
      </div>

      {/* Second section */}
      <div id="about-us-section" className="min-h-screen flex relative w-full mt-10" style={{ backgroundImage: 'url(/assets/track33.png)', backgroundSize: '85% 100%', backgroundPosition: 'right', backgroundRepeat: 'no-repeat' }}>
        <div className="w-full absolute bottom-0 right-0 z-40">
          <img src="/assets/car22.png" alt="Track" className="absolute bottom-0 right-0 md:w-[50%]" />
        </div>
        <div className="w-[70%] md:w-[50%] flex flex-col md:px-4 items-center bg-opacity-0">
          <div className='flex flex-col items-center mb-3 md:mb-5'>
            <img src="/assets/logolandingpage2.png" alt="Logo" className="md:hidden block h-24 mb-2 px-4" />
            <div className="w-[60%] md:hidden h-[2vh] bg-black-to-white-top"></div>
            <h1 className="md:hidden block text-3xl text-black font-bold font-serif">How This Works</h1>
          </div>

          <div className="items-start bg-transparent hidden md:block md:text-lg xl:text-3xl md:mt-4 xl:mt-12 text-sm text-gray-800 mb-2 font-serif md:px-2 ml-20">
            <p className="text-lg mb-6 text-left xl:text-3xl">
              KadereConnect simplifies how you manage your vehicles and drivers across Kenya. Whether you own one vehicle or a hundred, stay in control of your operations.
            </p>
            <div className="flex flex-col mb-4 px-4">
              <p className="text-md text-left xl:text-3xl xl:mb-3">
                <span className="font-bold">1. </span><span className="ml-2 font-bold">Sign Up for Free:</span> Create an account and enjoy 1-month full access to all features – no payment required.
              </p>
              <p className="text-md text-left xl:text-3xl xl:mb-3">
                <span className="font-bold">2. </span><span className="ml-2 font-bold">Add Your Vehicles:</span> Register unlimited vehicles with their key documents, service history, and payments.
              </p>
              <p className="text-md text-left xl:text-3xl xl:mb-3">
                <span className="font-bold">3. </span><span className="ml-2 font-bold">Hire Drivers Instantly:</span> Post driver jobs, receive applications, and vet using our built-in flagging system.
              </p>
              <p className="text-md text-left xl:text-3xl xl:mb-3">
                <span className="font-bold">4. </span><span className="ml-2 font-bold">Track Everything:</span> Get alerts for renewals, due payments, expiring documents, and driver updates.
              </p>
              <p className="text-md text-left xl:text-3xl">
                <span className="font-bold">5. </span><span className="ml-2 font-bold">Upgrade Anytime:</span> Choose a plan based on your vehicle count and hiring needs.
              </p>
            </div>
          </div>
          <TruncatedText />
          <a href="/jobs">
            <button className="md:mt-2 mb-2 xl:w-72 w-48">
              <img src="/assets/buttons/viewjobsbutton.png" alt="Get Started" className='md:w-[90%] xl:w-[100%] h-auto hidden md:block' />
            </button>
          </a>
        </div>
        <div className="w-[30%] md:w-[50%] flex flex-col items-center relative">
          <div className="w-full h-full flex relative z-10">
            <div className="w-[25%] md:block hidden bg-transparent" style={{ opacity: 0.7 }}></div>
            <div className="md:w-[75%] w-[100%] bg-orange-700 flex flex-col items-center" style={{ opacity: 0.8 }}>
              <img src="/assets/landingpageogo.png" alt="Logo" className="h-20 xl:h-32 mb-2 mt-8 px-4 md:block hidden" />
              <h1 className="text-2xl xl:text-5xl text-white font-bold font-arimo md:block hidden">How This Works</h1>
            </div>
            <div className="md:w-[0%] w-[0%] bg-transparent" style={{ opacity: 0.7 }}></div>
          </div>
        </div>
      </div>

      {/* Third section - Our Services */}
      <div id="services-section" className="flex relative w-full mt-10 min-h-screen md:min-h-screen mb-10">
        <div className="md:w-[50%] w-[30%] flex flex-col items-center relative">
          <div className="w-full h-full flex relative z-10">
            <div className="w-[25%] bg-transparent"></div>
            <div className="md:w-[55%] w-[100%] bg-orange-700 flex flex-col items-center h-full">
              <img src="/assets/landingpageogo.png" alt="Logo" className="hidden md:block h-20 xl:h-32 mb-4 mt-8 px-4" />
              <h1 className="hidden md:block text-2xl text-white xl:text-5xl font-bold font-serif">Our Services</h1>
            </div>
            <div className="w-[20%] bg-transparent"></div>
          </div>
        </div>
        <div className="md:w-[50%] w-[70%] flex flex-col items-start justify-start bg-opacity-0 md:px-8 py-4 md:py-20 relative pr-3">
          <div className='flex flex-col items-center mr-7 w-[80%] mb-2'>
            <img src="/assets/logolandingpage2.png" alt="Logo" className="md:hidden block h-16 px-4 mb-2" />
            <div className="w-[60%] md:hidden h-[2vh] bg-black-to-white-top"></div>
            <h1 className="md:hidden block text-3xl text-black font-bold font-serif">Our Services</h1>
          </div>

          {/* Service details */}
          <h2 className="text-xl xl:text-4xl xl:mt-20 font-bold text-start text-orange-700 font-serif">Vehicle Record Management</h2>
          <p className="md:text-lg xl:text-3xl xl:mt-3 text-sm text-gray-800 mb-1 md:mb-6 font-serif text-start">
            Store logbooks, insurance, NTSA docs, service history & expenses in one secure place.
          </p>

          <h2 className="text-xl xl:text-4xl font-bold text-start text-orange-700 font-serif">Driver Job Posts</h2>
          <p className="md:text-lg xl:text-3xl text-sm xl:mt-3 text-gray-800 mb-1 md:mb-6 font-serif text-start">
            Post jobs and receive applications with SMS/email notifications to find reliable drivers quickly.
          </p>

          <h2 className="text-xl xl:text-4xl font-bold text-start text-orange-700 font-serif">Driver Scorecards & History</h2>
          <p className="md:text-lg xl:text-3xl text-sm xl:mt-3 text-gray-800 mb-1 md:mb-6 font-serif text-start">
            Flag unreliable drivers and access community-generated performance data for better hiring decisions.
          </p>

          <h2 className="text-xl xl:text-4xl font-bold text-start text-orange-700 font-serif">Profitability Reports</h2>
          <p className="md:text-lg xl:text-3xl text-sm xl:mt-3 text-gray-800 mb-1 md:mb-6 font-serif text-start">
            See income vs. expenses across your fleet or individual cars with detailed analytics.
          </p>

          <h2 className="text-xl xl:text-4xl font-bold text-start text-orange-700 font-serif">Custom Lease Documents</h2>
          <p className="md:text-lg xl:text-3xl text-sm xl:mt-3 text-gray-800 mb-1 md:mb-6 font-serif text-start">
            Auto-generate lease agreements directly from the system with all necessary details.
          </p>

          <h2 className="text-xl xl:text-4xl font-bold text-start text-orange-700 font-serif">Reminders & Alerts</h2>
          <p className="md:text-lg xl:text-3xl text-sm xl:mt-3 text-gray-800 mb-1 md:mb-6 font-serif text-start">
            Get notified before insurance, inspections, and licenses expire to stay compliant.
          </p>

          <div className='md:flex justify-end xl:mt-6 mt-4 md:pl-20 items-end w-full z-50'>
            <a href='/jobs'>
              <button className="md:mt-6 w-36 md:w-60 xl:w-60 md:ml-0 ml-32 md:pl-8">
                <img src="/assets/buttons/viewcarsbutton.png" alt="Start Managing" className='w-[70%] xl:w-[100%] h-auto' />
              </button>
            </a>
          </div>

          {/* Mobile car image - now inside the services section */}
          <div className="md:hidden w-full flex justify-center mt-8">
            <img
              src="/assets/car3.png"
              alt="Track"
              className="w-[95%]"
            />
          </div>
        </div>

        {/* Desktop car image - remains unchanged */}
        <div className="hidden md:block w-full absolute bottom-0 left-0 z-10" style={{ pointerEvents: 'none' }}>
          <img
            src="/assets/car3.png"
            alt="Track"
            className="md:w-[50%]"
            style={{
              position: 'relative',
              marginTop: 'auto'
            }}
          />
        </div>
      </div>

      {/* Fourth section - Why Pick Us */}
      <div id="pricing-section" className="md:min-h-screen py-4 bg-cover" style={{ backgroundImage: 'url(/assets/backgroundorange.png)' }}>
        <div className="flex flex-col items-center text-center p-4">
          <img src="/assets/landingpageogo.png" alt="Logo" className="md:hidden block h-20 px-4 mb-4" />
          <h2 className="text-3xl xl:text-6xl font-bold md:mt-16 text-white font-serif">WHY PICK US</h2>
        </div>

        <div className="flex items-center xl:mt-10 justify-center">
          <div className="bg-gray-200 bg-opacity-65 xl:p-12 p-4 mx-4 md:mx-12 w-full md:w-[80%] flex flex-wrap rounded-xl h-[90%] md:h-auto">
            {data.map((item, index) => (
              <div key={index} className="w-[50%] md:flex flex-row items-start p-1 md:p-2 md:mt-4 md:mb-4">
                <img src={item.imgSrc} alt="Logo" className="md:w-[32%] w-[80%] mt-2" />
                <div className="ml-0 flex flex-col items-start md:mt-4">
                  <h2 className="md:text-lg xl:text-4xl text-orange-700 xl:mt-6 text-left font-bold font-serif">{item.heading}</h2>
                  <p className="text-gray-800 mt-2 xl:text-2xl text-left md:text-lg w-[95%] md:w-[80%] text-xs font-serif items-start">
                    {item.text.split(',').map((line, idx) => (
                      <span key={idx}>
                        {line}
                        {idx < item.text.split(',').length - 1 && <br />}
                      </span>
                    ))}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Contacts section */}
      <div id="contacts-section">
        <Contacts />
      </div>

      <Footer />
    </div>
  );
};

export default Home;
