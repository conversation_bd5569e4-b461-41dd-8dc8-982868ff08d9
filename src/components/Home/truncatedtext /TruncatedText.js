
import React, { useState } from 'react';

const TruncatedText = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const mobileText = [
    "KadereConnect simplifies how you manage your vehicles and drivers across Kenya. Whether you own one vehicle or a hundred, stay in control of your operations.",
    <>
      <strong>1. Sign Up for Free</strong> 
    </>,
    <>
      <strong>2. Add Your Vehicles</strong> 
    </>,
    <>
      <strong>3. Hire Drivers Instantly</strong> 
    </>,
    <>
      <strong>4. Track Everything</strong> 
    </>,
    <>
      <strong>5. Upgrade Anytime</strong> 
    </>
  ];

  const displayedLines = isExpanded ? mobileText : mobileText.slice(0, 5);

  const toggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className='md:hidden flex flex-col z-40 px-6'>
      {displayedLines.map((line, index) => (
        <p key={index} className='text-sm pb-2 text-gray-800 font-serif text-left'>
          {line}
        </p>
      ))}
      {mobileText.length > 5 && (
        <button 
          onClick={toggleExpansion} 
          className="text-red-500">
          {isExpanded ? 'show less' : 'read more'}
        </button>
      )}
    </div>
  );
};

export default TruncatedText;


