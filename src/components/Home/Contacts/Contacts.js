import React, { useState } from "react";
import axios from "axios";
import { BASE_URL } from "../../../services/config";

function Contacts() {
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    phone_number: "",
    email: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState({ show: false, isError: false, message: "" });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await axios.post(`${BASE_URL}api/enquiry/`, formData);
      setSubmitStatus({
        show: true,
        isError: false,
        message: "Thank you for your enquiry. We will get back to you soon."
      });
      setFormData({
        first_name: "",
        last_name: "",
        phone_number: "",
        email: "",
        message: ""
      });
    } catch (error) {
      setSubmitStatus({
        show: true,
        isError: true,
        message: error.response?.data?.message || "Failed to submit enquiry. Please try again."
      });
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus({ show: false, isError: false, message: "" }), 5000);
    }
  };

  return (
    <div> 
      {submitStatus.show && (
        <div className={`fixed top-5 left-1/2 transform -translate-x-1/2 ${submitStatus.isError ? 'bg-red-500' : 'bg-green-500'
          } text-white p-4 rounded shadow-lg z-[9999] font-serif text-sm md:text-base`}>
          {submitStatus.message}
        </div>
      )}
      {/* 5th page*/}
      <div className="md:min-h-screen md:mb-2 xl:mb-10">
        <div className="text-center">
          <h2 className="text-3xl xl:text-5xl font-bold xl:mt-20  mt-16 text-orange-700 font-serif">
            MAKE AN ENQUIRY
          </h2>
          <p className="font-serif xl:text-2xl xl:mt-6  mt-4 px-2">
            For any driver/partner related enquiries or requests please fill out
            the following form and we'll get back to you as soon as possible.
            <br /> Alternatively contact us directly via telephone or mobile.
          </p>
        </div>
        <div className="flex flex-col items-center justify-center w-full relative mt-8">
          <div className="absolute top-0 w-full h-[100vh] bg-cover flex">
            <div
              className="w-1/2 h-full hidden md:block  bg-no-repeat"
              style={{
                backgroundImage: "url('/assets/car4right.png')",
                backgroundSize: "contain",
              }}
            ></div>
            <div
              className="w-1/2 h-full hidden md:block  bg-no-repeat"
              style={{
                backgroundImage: "url('/assets/car4left.png')",
                backgroundSize: "contain",
              }}
            ></div>
          </div>

          <div className="relative flex flex-col items-center justify-center w-full px-2">
            <div className="bg-red-200 bg-opacity-80 xl:py-16 py-8 md:px-20 px-4 mx-4 md:mx-12 w-full md:w-[70%] rounded-xl">
              <form onSubmit={handleSubmit} className="grid grid-cols-2 md:grid-cols-2 md:gap-4 gap-2">
                <div className="flex flex-col items-start">
                  <label
                    className="block text-gray-700 xl:text-lg text-sm mb-2 font-serif"
                    htmlFor="firstName"
                  >
                    FIRST NAME:*
                  </label>
                  <input
                    className="appearance-none block w-full bg-white text-gray-700 border font-serif rounded-lg xl:py-5 py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500"
                    id="firstName"
                    name="first_name"
                    type="text"
                    value={formData.first_name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="flex flex-col items-start">
                  <label
                    className="block text-gray-700 xl:text-lg text-sm mb-2 font-serif"
                    htmlFor="lastName"
                  >
                    LAST NAME:*
                  </label>
                  <input
                    className="appearance-none block w-full bg-white text-gray-700 font-serif border rounded-lg py-3 xl:py-5 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500"
                    id="lastName"
                    name="last_name"
                    type="text"
                    value={formData.last_name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="flex flex-col items-start">
                  <label
                    className="block text-gray-700 xl:text-lg text-sm mb-2 font-serif"
                    htmlFor="phone"
                  >
                    PHONE NUMBER:*
                  </label>
                  <input
                    className="appearance-none block w-full bg-white text-gray-700 border font-serif rounded-lg py-3 xl:py-5 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500"
                    id="phone"
                    name="phone_number"
                    type="tel"
                    value={formData.phone_number}
                    onChange={handleChange}
                    placeholder="+254XXXXXXXXX"
                    required
                  />
                </div>

                <div className="flex flex-col items-start">
                  <label
                    className="block text-gray-700 xl:text-lg text-sm mb-2 font-serif"
                    htmlFor="email"
                  >
                    EMAIL:*
                  </label>
                  <input
                    className="appearance-none block w-full bg-white text-gray-700 border font-serif rounded-lg py-3 xl:py-5 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500"
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="flex flex-col items-start col-span-full">
                  <label
                    className="block text-gray-700 text-sm xl:text-lg mb-2 font-serif"
                    htmlFor="message"
                  >
                    MESSAGE:*
                  </label>
                  <textarea
                    className="appearance-none block w-full bg-white text-gray-700 border rounded-lg py-3 xl:py-12 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500 h-40"
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    placeholder="Type your message here..."
                  ></textarea>
                </div>

                <div className="flex justify-end col-span-full">
                  <button
                    className="bg-orange-700 text-white xl:text-3xl py-3 px-8 rounded-3xl font-serif hover:bg-orange-800 focus:outline-none focus:bg-orange-600 disabled:opacity-50"
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "SUBMITTING..." : "SUBMIT"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Contacts;
