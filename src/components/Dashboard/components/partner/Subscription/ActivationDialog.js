import React, { useState, useEffect } from 'react';
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';

const ActivationDialog = ({ onActivate, onRequestDemo, onClose, setShowPaymentPlans }) => {
  const [isActivating, setIsActivating] = useState(false);
  const [error, setError] = useState(null);
  const [showPopup, setShowPopup] = useState(false);
  const [profile, setProfile] = useState(null);
  const [vehicles, setVehicles] = useState([]);

  // Fetch profile data
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const response = await api.get(`${BASE_URL}api/users/me/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        setProfile(response);
      } catch (error) {
        console.error('Error fetching profile:', error);
        setError('Failed to load profile data');
      }
    };

    fetchProfile();
  }, []);

  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const response = await api.get(`${BASE_URL}api/vehicles/`, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('Vehicles data:', response); 
        setVehicles(response || []);
      } catch (error) {
        console.error('Error fetching vehicles:', error);
        setVehicles([]);
      }
    };

    fetchVehicles();
  }, []);

  const handleActivate = async () => {
    setIsActivating(true);
    try {
      const token = localStorage.getItem('token');
      
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const vehicleCount = vehicles.length;
      console.log('Total Vehicles:', vehicleCount); // Debug log

      const requestBody = {
        partner: profile.partner_details.id,
        plan_type: 'Free',
        vehicle_count: vehicleCount 
      };

      console.log('Request body:', requestBody); // Debug log

      const response = await api.post(
        `${BASE_URL}api/subscriptions/activate/`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response) {
        setShowPopup(true);
        setTimeout(() => {
          setShowPopup(false);
          setShowPaymentPlans(true);
          onClose();
        }, 3000);
      }
    } catch (error) {
      console.error('Error activating subscription:', error);
      setError(error.response?.data?.message || 'Failed to activate subscription');
    } finally {
      setIsActivating(false);
    }
  };

  if (profile?.subscription?.status === 'active') return null;

  return (
    <>
      {showPopup && (
        <div className="fixed top-26 font-serif left-1/2 transform -translate-x-1/2 bg-green-500 text-white p-4 rounded shadow-md z-50">
          Free plan activated successfully
        </div>
      )}
      
      <div className="fixed inset-0 z-[1002] flex items-center justify-center bg-black/60 p-4">
        <div className="w-full max-w-md rounded-lg bg-white p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded font-serif">
              {error}
            </div>
          )}
          
          <p className="mb-4 text-center text-lg font-serif">
            You're just one step away from posting your first job!{' '}
            <span className="font-semibold text-orange-600 font-serif">Activate</span> your Free
            Plan now to unlock job postings, vehicle listings, and more.
          </p>
          
          <p className="mb-6 text-center text-sm text-gray-600 font-serif">
            Prefer a walkthrough?{' '}
            <button 
              onClick={onRequestDemo}
              className="text-orange-600 hover:underline font-serif"
            >
              Request a demo
            </button>
          </p>
          
          <div className="flex flex-col gap-3 sm:flex-row sm:gap-4">
            <button
              onClick={onClose}
              className="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 font-serif"
              disabled={isActivating}
            >
              MAYBE LATER
            </button>
            <button
              onClick={handleActivate}
              disabled={isActivating}
              className="w-full rounded-md bg-orange-600 px-4 py-2 text-sm font-medium text-white hover:bg-orange-700 disabled:opacity-50 font-serif"
            >
              {isActivating ? 'ACTIVATING...' : 'ACTIVATE FREE PLAN'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ActivationDialog;