import React, { useState, useEffect } from "react";
import MakePayment from "./MakePayment";
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';

const SetPaymentPlan = ({ onBack, partnerId }) => {
  const [showPayment, setShowPayment] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [currentPlan, setCurrentPlan] = useState('');
  const [hasNoActivePlan, setHasNoActivePlan] = useState(false);

  useEffect(() => {
    const fetchCurrentPlan = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await api.get(`${BASE_URL}api/subscriptions/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response) {
          // Set current plan only if response exists and is active
          const isActive = response.status === 'active' ||
            response.latest_payment?.status === 'success';

          if (isActive) {
            setCurrentPlan(response.plan_type.toUpperCase());
          } else {
            setHasNoActivePlan(true);
          }
        }
      } catch (error) {
        console.error('Error fetching current plan:', error);
        if (error.response?.status === 404) {
          setHasNoActivePlan(true);
          setCurrentPlan('');
        }
      }
    };

    fetchCurrentPlan();
  }, []);

  // Helper function to render plan header
  const renderPlanHeader = (planName) => {
    const isCurrent = currentPlan === planName.toUpperCase() && !hasNoActivePlan;
    return (
      <header className="px-6 pt-6 pb-4 border-b-2 border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-sm font-bold tracking-wide uppercase font-serif">
            {planName}
          </h2>
          {isCurrent && (
            <span className="text-xs font-semibold text-green-600 bg-green-100 px-2 py-1 rounded-full font-serif">
              CURRENT
            </span>
          )}
        </div>
      </header>
    );
  };

  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan);
    setShowPayment(true);
  };

  if (showPayment) {
    return (
      <MakePayment
        onClose={() => setShowPayment(false)}
        planType={selectedPlan}
        partnerId={partnerId}
      />
    );
  }

  return (
    <div className="w-full min-h-screen bg-gray-100 flex justify-center items-start py-8 px-4 md:px-8 lg:px-12">
      <div className="w-full max-w-[1300px] bg-white/90 backdrop-blur-md rounded-2xl shadow-lg px-6 md:px-10 lg:px-14 py-8 font-serif">
        <div className="flex items-center gap-4 mb-10">
          <button
            onClick={onBack}
            className="flex items-center text-black hover:text-gray-700 transition-colors font-serif"
            aria-label="Go back"
          >
            <svg
              className="w-8 h-8 md:w-10 md:h-10"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h1 className="text-3xl md:text-4xl font-extrabold font-serif">
            Select Your Plan
          </h1>
        </div>

        {hasNoActivePlan && (
          <div className="mb-8 p-4 bg-orange-50 border border-orange-200 rounded-lg text-center font-serif">
            <p className="text-orange-800">
              You have no active plan. Choose either Standard or Pro to access our services.
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 lg:gap-10">
          {/* Free Plan */}
          <article className="bg-[#FBFBFB] rounded-xl border border-gray-200 shadow-sm flex flex-col h-full overflow-hidden font-serif">
            {renderPlanHeader('Free')}
            <ul className="flex flex-col gap-4 px-6 py-6 text-sm flex-grow font-serif">
              {[
                "Manage unlimited vehicles",
                "Post up to 2 jobs",
                "Receive email/SMS reminders and reports",
              ].map((item) => (
                <li key={item} className="flex items-start gap-3 text-left">
                  <svg
                    className="w-5 h-5 flex-shrink-0 mt-[2px] text-black"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
            <div className="px-6 pt-0 pb-6 mt-auto text-sm leading-relaxed font-serif">
              <p className="font-bold text-orange-600 mb-1">Note:</p>
              <p className="italic">
                This Plan allows access to all features for 1 month with no payment required.
              </p>
            </div>
          </article>

          {/* Standard Plan */}
          <article className="bg-[#FBFBFB] rounded-xl border border-gray-200 shadow-sm flex flex-col h-full overflow-hidden font-serif">
            {renderPlanHeader('Standard')}
            <div className="px-6 pt-6">
              <p className="text-3xl font-extrabold text-orange-600 leading-none font-serif">
                KSH 1000/= <span className="text-sm font-normal text-gray-600 ml-1">PER VEHICLE</span>
              </p>
            </div>
            <ul className="flex flex-col gap-4 px-6 py-6 text-sm flex-grow font-serif">
              {[
                "Full vehicle record keeping",
                "1 job post per month",
                "Business analytics & insights",
                "Alerts",
              ].map((item) => (
                <li key={item} className="flex items-start gap-3 text-left">
                  <svg
                    className="w-5 h-5 flex-shrink-0 mt-[2px] text-black"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
            <section className="px-6 pb-6 mt-auto text-sm space-y-2 font-serif">
              <p className="font-bold text-orange-600 -ml-1">Discounts:</p>
              <p>
                <span className="font-bold text-black">Quarterly Payment:</span>{" "}
                <span className="text-black-600">KES 2,700/car (10% off)</span>
              </p>
              <p>
                <span className="font-bold text-black">Annual Payment:</span>{" "}
                <span className="text-black-600">KES 9,600/car (20% off)</span>
              </p>
            </section>
            <div className="px-6 pb-6">
              <button
                onClick={() => handleSelectPlan("Standard")}
                className="w-full bg-orange-600 text-white py-3 rounded-md hover:bg-orange-700 transition font-semibold tracking-wide font-serif"
              >
                Select Standard Plan
              </button>
            </div>
          </article>

          {/* Pro Plan */}
          <article className="bg-[#FBFBFB] rounded-xl border border-gray-200 shadow-sm flex flex-col h-full overflow-hidden font-serif">
            {renderPlanHeader('Pro')}
            <div className="px-6 pt-6">
              <p className="text-3xl font-extrabold text-orange-600 leading-none">
                KSH 1800/= <span className="text-sm font-normal text-gray-600 ml-1">PER VEHICLE</span>
              </p>
            </div>
            <ul className="flex flex-col gap-4 px-6 py-6 text-sm flex-grow font-serif">
              {[
                "Unlimited job postings",
                "Advanced reports (profitability, driver scorecards)",
                "Access to flagged driver data",
                "Lease document builder",
              ].map((item) => (
                <li key={item} className="flex items-start gap-3 text-left">
                  <svg
                    className="w-5 h-5 flex-shrink-0 mt-[2px] text-black"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
            <section className="px-6 pb-6 mt-auto text-sm space-y-2 font-serif">
              <p className="font-bold text-orange-600 -ml-1">Discounts:</p>
              <p>
                <span className="font-bold text-black">Quarterly Payment:</span>{" "}
                <span className="text-black-600">KES 4,860/car (10% off)</span>
              </p>
              <p>
                <span className="font-bold text-black">Annual Payment:</span>{" "}
                <span className="text-black-600">KES 17,280/car (20% off)</span>
              </p>
            </section>
            <div className="px-6 pb-6">
              <div className="px-6 pb-6">
                <button
                  onClick={() => handleSelectPlan("Pro")}
                  disabled
                  className="w-full bg-orange-600 text-white py-3 rounded-md transition font-semibold tracking-wide font-serif opacity-50 cursor-not-allowed"
                >
                  Select Pro Plan
                </button>
              </div>

            </div>
          </article>
        </div>
      </div>
    </div>
  );
};
export default SetPaymentPlan;
