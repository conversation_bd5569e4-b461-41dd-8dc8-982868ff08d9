import React, { useState, useEffect } from 'react';
import ManagePlan from './ManagePlan';
import PaymentHistory from './PaymentHistory';
import SetPaymentPlan from './SetPaymentPlan';
import api from '../../../../../services/api';
import { BASE_URL } from '../../../../../services/config';

const PaymentAndPlans = ({ onBack, partnerId }) => {
  const [showManagePlan, setShowManagePlan] = useState(false);
  const [showPaymentHistory, setShowPaymentHistory] = useState(false);
  const [showSetPaymentPlan, setShowSetPaymentPlan] = useState(false);
  const [planDetails, setPlanDetails] = useState({
    plan_type: '',
    expiry_date: '',
    start_date: '',
    vehicle_count: 0,
    amount: '0.00',
    status: '',
    billing_cycle: null
  });
  const [userName, setUserName] = useState(''); // State for user name

     // Independent function to fetch user details
  const fetchUserDetails = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await api.get(`${BASE_URL}api/users/me/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response && response.first_name && response.last_name) {
        setUserName(`${response.first_name} ${response.last_name}`);
      } else {
        setUserName('User Name Not Available');
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
      setUserName('User Name Not Available');
    }
  };

  useEffect(() => {
    const fetchPlanDetails = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await api.get(`${BASE_URL}api/subscriptions/subscription-history/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response) {
          const latestSubscription = Array.isArray(response) ? 
            response.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0] 
            : response;

          if (latestSubscription) {
            const isExpired = new Date(latestSubscription.expiry_date) < new Date();
            
            const newPlanDetails = {
              plan_type: latestSubscription.plan_type || 'No Plan',
              expiry_date: latestSubscription.expiry_date || 'N/A',
              start_date: latestSubscription.start_date,
              vehicle_count: latestSubscription.vehicle_count,
              amount: latestSubscription.amount,
              status: latestSubscription.status,
              billing_cycle: latestSubscription.billing_cycle,
              latest_payment: latestSubscription.latest_payment,
              is_expired: isExpired
            };

            setPlanDetails(newPlanDetails);
          }
        } else {
          setPlanDetails({
            plan_type: '',
            expiry_date: '',
            start_date: '',
            vehicle_count: 0,
            amount: '0.00',
            status: '',
            billing_cycle: null
          });
        }
      } catch (error) {
        console.error('Error fetching subscription history:', error);
        setPlanDetails({
          plan_type: '',
          expiry_date: '',
          start_date: '',
          vehicle_count: 0,
          amount: '0.00',
          status: '',
          billing_cycle: null,
          is_expired: false
        });
      }
    };

    fetchPlanDetails();
     fetchUserDetails();
  }, []);

  useEffect(() => {
    console.log('Current plan details:', planDetails);
  }, [planDetails]);

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Plan status badge
  const PlanStatusBadge = ({ status }) => {
    const getStatusColor = () => {
      if (planDetails.is_expired) {
        return 'bg-red-100 text-red-800';
      }
      if (planDetails.status === 'cancelled') {
        return 'bg-yellow-100 text-yellow-800';
      }
      return planDetails.latest_payment?.status === 'success' ? 
        'bg-green-100 text-green-800' : 
        'bg-yellow-100 text-yellow-800';
    };

    const getStatusText = () => {
      if (planDetails.is_expired) {
        return 'EXPIRED';
      }
      if (planDetails.status === 'cancelled') {
        return 'CANCELLED - ACTIVE UNTIL EXPIRY';
      }
      return planDetails.latest_payment?.status === 'success' ? 'ACTIVE' : planDetails.status.toUpperCase();
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </span>
    );
  };

  const handleManagePlan = () => {
    setShowManagePlan(true);
  };

  const handleApplyVoucher = () => {
    // Handle voucher code application
  };



  const renderContent = () => {
    if (!planDetails.plan_type) {
      return (
        <div className="w-full max-w-[1200px] space-y-6 font-serif">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-black font-serif">PAYMENT & PLANS</h1>
            <button 
              onClick={() => setShowPaymentHistory(true)}
              className="text-sm text-orange-600 font-semibold hover:underline font-serif"
            >
              VIEW PAYMENT HISTORY
            </button>
          </div>
          <div className="bg-white p-8 rounded-xl shadow-md text-center font-serif">
            <p className="text-lg font-medium text-gray-700 font-serif">You have no active subscription</p>
            <button
              onClick={() => setShowSetPaymentPlan(true)}
              className="mt-4 bg-orange-600 text-white font-bold px-6 py-2 rounded-md hover:bg-orange-700 font-serif"
            >
              View Plans
            </button>
          </div>
        </div>
      );
    }

  

    return (
      <div className="w-full space-y-6 font-serif  ">
        <h1 className="text-3xl font-bold text-black font-serif">PAYMENT & PLANS</h1>

        <div className="flex flex-col md:flex-row gap-4">
          {/* Plan Card */}
          <div className="flex-1 bg-white p-6 rounded-xl shadow-md font-serif">
            <div className="flex justify-between items-start">
              <p className="text-sm font-bold text-gray-700 font-serif">YOUR PLAN</p>
              <PlanStatusBadge status={planDetails.status} />
            </div>
            <h2 className="text-lg font-semibold mt-2 font-serif">
              {planDetails.plan_type.toUpperCase()} PLAN
            </h2>
            <div className="mt-2 space-y-1">
              <p className="text-sm text-gray-500 font-serif">
                Start Date: {formatDate(planDetails.start_date)}
              </p>
              <p className="text-sm text-gray-500 font-serif">
                Expiry Date: {formatDate(planDetails.expiry_date)}
              </p>
              <p className="text-sm text-gray-500 font-serif">
                Vehicle Count: {planDetails.vehicle_count}
              </p>
            </div>
            <div className="flex justify-between items-center mt-4">
              <button 
                onClick={handleManagePlan}
                className="text-sm text-orange-600 font-semibold hover:underline font-serif"
              >
                Manage Plan
              </button>
              <span className="text-sm font-semibold text-black font-serif">
                KSH {parseFloat(planDetails.amount).toFixed(2)} {planDetails.billing_cycle || 'pm'}
              </span>
            </div>
          </div>

          {/* Payment Method Card */}
          <div className="flex-1 bg-white p-6 rounded-xl shadow-md font-serif">
            <p className="text-sm font-bold text-gray-700 font-serif">PAYMENT METHOD</p>
            <p className="text-base mt-2 font-medium font-serif">{userName}</p>
            <button className="text-sm text-orange-600 font-semibold hover:underline mt-2 font-serif">
              SET PAYMENT METHOD
            </button>
          </div>
        </div>

        {/* Voucher Section */}
        <div className="bg-white p-6 rounded-xl shadow-md font-serif">
          <p className="text-sm font-bold text-gray-700 mb-3 font-serif">
            HAVE A VOUCHER OR PROMO CODE
          </p>
          <div className="flex flex-col md:flex-row gap-4">
            <input
              type="text"
              placeholder=""
              className="flex-1 border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 font-serif"
            />
            <button
              onClick={handleApplyVoucher}
              className="bg-orange-600 text-white font-bold px-8 py-2 rounded-md hover:bg-orange-700 font-serif"
            >
              APPLY
            </button>
          </div>
        </div>

        {/* Upcoming Payments Section */}
        <div className="bg-white p-6 rounded-xl shadow-sm font-serif">
          <div className="flex justify-between items-center mb-2">
            <p className="text-sm font-bold text-gray-700 font-serif">UPCOMING PAYMENTS</p>
            <button 
              onClick={() => setShowPaymentHistory(true)}
              className="text-sm text-orange-600 font-semibold hover:underline font-serif"
            >
              VIEW PAYMENT HISTORY
            </button>
          </div>
          <p className="text-base font-medium text-gray-800 font-serif">NO UPCOMING PAYMENTS</p>
        </div>
      </div>
    );
  };
  if (showSetPaymentPlan) {
    return <SetPaymentPlan 
      onBack={() => setShowSetPaymentPlan(false)} 
      partnerId={partnerId}  
    />;
  }

  if (showPaymentHistory) {
    return <PaymentHistory onBack={() => setShowPaymentHistory(false)} />;
  }

  if (showManagePlan) {
    return <ManagePlan 
      onBack={() => setShowManagePlan(false)}
      partnerId={partnerId}  
    />;
  }

  return (
    <div className="w-full min-h-screen bg-gray-100 px-4 py-6">
      {renderContent()}
    </div>
  );
};

export default PaymentAndPlans;




