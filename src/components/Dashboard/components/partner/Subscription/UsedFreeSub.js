import React from 'react';

const UsedFreeSub = ({ isOpen, onClose, setShowPaymentPlans }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 font-serif">
        <div className="text-center">
          {/* Warning Icon */}
          <div className="mb-6">
            <svg 
              className="mx-auto h-12 w-12 text-orange-500" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
              />
            </svg>
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold mb-4 font-serif">
            Free Trial Already Used
          </h2>

          {/* Message */}
          <p className="text-gray-600 mb-6 font-serif">
            You have already used your free trial subscription. Please choose a paid plan to continue using our services.
          </p>

          {/* Single Action Button */}
          <button
            onClick={() => {
              setShowPaymentPlans(true);
              onClose();
            }}
            className="w-full px-6 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors font-serif"
          >
            VIEW PAYMENT PLANS
          </button>
        </div>
      </div>
    </div>
  );
};

export default UsedFreeSub;