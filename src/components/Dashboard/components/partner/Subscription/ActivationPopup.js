import React from 'react';

const ActivationPopup = ({ onActivate, onRequestDemo, setShowPaymentPlans }) => {
  const handleActivate = () => {
    setShowPaymentPlans(true);
    onActivate(); // Close the activation popup
  };

  return (
    <div className="fixed inset-0 z-[1002] flex items-center justify-center bg-black/60 p-4">
      <div className="w-full max-w-md rounded-lg bg-white p-6">
        <p className="mb-4 text-center text-lg">
          You're just one step away from Managing Your Vehicles!{' '}
          <span className="font-semibold text-orange-600">Activate</span> your Free
          Plan now to unlock job postings, vehicle listings, and more.
        </p>
        
        <p className="mb-6 text-center text-sm text-gray-600">
          Prefer a walkthrough?{' '}
          <button 
            onClick={onRequestDemo}
            className="text-orange-600 hover:underline"
          >
            Request a demo
          </button>
        </p>
        
        <div className="flex flex-col gap-3 sm:flex-row sm:gap-4">
          <button
            onClick={onRequestDemo}
            className="w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            REQUEST DEMO
          </button>
          <button
            onClick={handleActivate}
            className="w-full rounded-md bg-orange-600 px-4 py-2 text-sm font-medium text-white hover:bg-orange-700"
          >
            ACTIVATE FREE PLAN
          </button>
        </div>
      </div>
    </div>
  );
};

export default ActivationPopup;