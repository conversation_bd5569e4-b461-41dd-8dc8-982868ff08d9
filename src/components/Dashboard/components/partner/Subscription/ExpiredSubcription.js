import React from 'react';

const ExpiredSubscription = ({ isOpen, onClose, setShowPaymentPlans, planType }) => {
  if (!isOpen) return null;

  const handleRenew = () => {
    setShowPaymentPlans(true);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 font-serif">
        <div className="text-center">
          {/* Warning Icon */}
          <div className="mb-6">
            <svg 
              className="mx-auto h-12 w-12 text-red-500" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold mb-4 font-serif">
            Subscription Expired
          </h2>

          {/* Message with Highlighted Action */}
          <p className="text-gray-600 mb-2 font-serif">
            Your {planType} subscription has expired.
          </p>
          <p className="text-gray-800 font-medium mb-6 font-serif">
            Click <span className="text-orange-600 font-bold">RENEW</span> to continue accessing our services.
          </p>

          {/* Action Button with Enhanced Styling */}
          <button
            onClick={handleRenew}
            className="w-full px-6 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700 
                     transition-all transform hover:scale-[1.02] active:scale-[0.98]
                     font-bold tracking-wide shadow-lg hover:shadow-xl font-serif
                     animate-pulse hover:animate-none"
          >
            RENEW SUBSCRIPTION
          </button>

          {/* Additional Note */}
          <p className="mt-4 text-sm text-gray-500 font-serif">
            Renewing now will restore immediate access to all features
          </p>
        </div>
      </div>
    </div>
  );
};

export default ExpiredSubscription;