import React, { useState, useEffect } from "react";
import * as Recharts from "recharts";
import api from "../../../../services/api";
import axios from "axios";
import { BASE_URL } from "../../../../services/config";
import SpinnerTwo from "../../../spinner/SpinnerTwo";


export default function Reports() {
  const {
    BarChart,
    Bar,
    // LineChart,
    // Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
  } = Recharts;


  // const [graphType, setGraphType] = useState("revenue");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedType, setSelectedType] = useState("");
  const [selectedVehicle, setSelectedVehicle] = useState("");
  const [transactionData, setTransactionData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [vehicles, setVehicles] = useState([]);
  const [categories, setCategories] = useState([]);
  const [totalRevenue, setTotalRevenue] = useState(0);
  const [totalExpenditure, setTotalExpenditure] = useState(0);
  const [totalProfit, setTotalProfit] = useState(0);
  const [dailyTotals, setDailyTotals] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Filter state management
  const [tempStartDate, setTempStartDate] = useState("");
  const [tempEndDate, setTempEndDate] = useState("");
  const [tempSelectedCategory, setTempSelectedCategory] = useState("");
  const [tempSelectedType, setTempSelectedType] = useState("");
  const [tempSelectedVehicle, setTempSelectedVehicle] = useState("");

  // Edit and delete functionality state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editTransaction, setEditTransaction] = useState(null);
  const [editForm, setEditForm] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [transactionToDelete, setTransactionToDelete] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  // Apply search filters
  const handleSearch = () => {
    setStartDate(tempStartDate);
    setEndDate(tempEndDate);
    setSelectedCategory(tempSelectedCategory);
    setSelectedType(tempSelectedType);
    setSelectedVehicle(tempSelectedVehicle);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Clear all filters
  const handleClearFilters = () => {
    setTempStartDate("");
    setTempEndDate("");
    setTempSelectedCategory("");
    setTempSelectedType("");
    setTempSelectedVehicle("");
    setStartDate("");
    setEndDate("");
    setSelectedCategory("");
    setSelectedType("");
    setSelectedVehicle("");
    setCurrentPage(1);
  };

  // Fetch financial report data
  const fetchFinancialReport = async () => {
    try {
      setLoading(true);

        // Create query parameters based on filters and pagination
        const params = new URLSearchParams();
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (selectedVehicle) params.append('vehicle', selectedVehicle);
        if (selectedType) params.append('type', selectedType.toLowerCase());
        if (selectedCategory) params.append('item_name', selectedCategory);

        // Add ordering to show latest reports first
        params.append('ordering', '-date');

        // Add pagination parameters
        params.append('page', currentPage.toString());
        params.append('page_size', pageSize.toString());

      // Fetch financial report with filters
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await axios.get(
        `${BASE_URL}api/financial-report/?${params.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Extract data from response (now paginated)
      const {
        detailed_report,
        daily_totals,
        total_revenue,
        total_expenditure,
        total_profit,
        count,
        total_pages,
        current_page,
        page_size
      } = response.data;

      // Transform detailed_report for the component
      const transformedData = detailed_report.map(item => ({
        id: item.id,
        date: item.date,
        amount: parseFloat(item.amount),
        type: item.type.toLowerCase(),
        category: item.type === 'Revenue' ? 'Payment' : (item.item_name || ''),
        description: item.type === 'Revenue' ? item.confirmation_message : item.description,
        car: item.vehicle
      }));

      setTransactionData(transformedData);
      setDailyTotals(daily_totals);
      setTotalRevenue(total_revenue);
      setTotalExpenditure(total_expenditure);
      setTotalProfit(total_profit);

      // Set pagination data
      setTotalCount(count || 0);
      setTotalPages(total_pages || 1);
      setCurrentPage(current_page || 1);
      setPageSize(page_size || 10);

      // Extract unique categories
      const uniqueCategories = [...new Set(
        transformedData
          .filter(item => item.type === 'expenditure' && item.category)
          .map(item => item.category)
      )];
      setCategories(uniqueCategories);

      setLoading(false);
    } catch (err) {
      console.error('Error fetching financial report:', err);

      if (err.response && err.response.data && err.response.data.error) {
        setError(err.response.data.error); 
      } else if (err.response && err.response.data) {
        setError(JSON.stringify(err.response.data));
      } else {
        setError(err.message || 'Failed to load financial report');
      }

      setLoading(false);
    }
  };

  // Fetch vehicle list for the filter
  const fetchVehicles = async () => {
    try {
      const response = await api.get('api/vehicles/');
      setVehicles(response);
    } catch (err) {
      console.error('Error fetching vehicles:', err);
    }
  };

  // Initial load and when page changes
  useEffect(() => {
    fetchFinancialReport();
    fetchVehicles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startDate, endDate, selectedVehicle, selectedType, selectedCategory, currentPage]);

  // Initialize temp filters with current filter values
  useEffect(() => {
    setTempStartDate(startDate);
    setTempEndDate(endDate);
    setTempSelectedCategory(selectedCategory);
    setTempSelectedType(selectedType);
    setTempSelectedVehicle(selectedVehicle);
  }, [startDate, endDate, selectedCategory, selectedType, selectedVehicle]);

  // Filter data based on selected filters - now done through API params
  const filteredData = transactionData;

  // Process data for revenue/expenditure bar graph
  const barGraphData = [
    {
      category: "Revenue",
      amount: totalRevenue,
    },
    {
      category: "Expenditure",
      amount: totalExpenditure,
    },
    {
      category: "Profit",
      amount: totalProfit,
    },
  ];

  // Create line chart data from daily totals
  const profitLineData = dailyTotals ?
    Object.entries(dailyTotals).map(([date, values]) => ({
      date,
      profit: values.profit,
      revenue: values.revenue,
      expenditure: values.expenditure
    })) : [];

  // Sort line chart data by date
  profitLineData.sort((a, b) => new Date(a.date) - new Date(b.date));

  // Check if there's an error during loading
  if (error) {
    return (
      <div className="bg-white bg-opacity-50 rounded-xl shadow-lg p-6 m-4 w-[90%]">
        <div className="flex justify-center items-center min-h-[200px]">
          <p className="text-xl font-bold text-red-600 font-serif">
            {error}
          </p>
        </div>
      </div>
    );
  }

  // Show loading state
  if (loading) {
    return (
      <div className="bg-white bg-opacity-50 rounded-xl shadow-lg p-6 m-4 w-[90%] flex justify-center items-center min-h-[200px]">
        <SpinnerTwo />
      </div>
    );
  }

  // Handler for Edit button
  const handleEdit = (transaction) => {
    setEditTransaction(transaction);
    setEditForm({
      date: transaction.date,
      amount: transaction.amount,
      type: transaction.type,
      car: transaction.car,
      description: transaction.description,
      category: transaction.category || "",
    });
    setShowEditModal(true);
  };

  // Handle form changes:
  const handleEditFormChange = (e) => {
    const { name, value } = e.target;
    setEditForm((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle save (update logic):
  const handleSaveEdit = async () => {
    if (!editTransaction || !editTransaction.id) return;

    setLoading(true); 
    const endpoint =
      editForm.type === "revenue"
        ? `${BASE_URL}api/revenues/${editTransaction.id}/`
        : `${BASE_URL}api/expenditures/${editTransaction.id}/`;

    const token = localStorage.getItem("token");
    try {
      const response = await axios.patch(
        endpoint,
        {
          date: editForm.date,
          amount: editForm.amount,
          car: editForm.car,
          description: editForm.description,
          ...(editForm.type === "expenditure" && { item_name: editForm.category }),
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.status === 200) {
        setTransactionData((prev) =>
          prev.map((item) =>
            item === editTransaction
              ? { ...item, ...editForm }
              : item
          )
        );
        setShowEditModal(false);
        setEditTransaction(null);
        setSuccessMessage("Transaction updated successfully!");
        setTimeout(() => setSuccessMessage(""), 3000);
        await fetchFinancialReport(); 
      } else {
        setErrorMessage("Update failed. Server responded with status: " + response.status);
        setTimeout(() => setErrorMessage(""), 3000);
      }
    } catch (err) {
      setErrorMessage("Failed to update transaction.");
      setTimeout(() => setErrorMessage(""), 3000);
    }
    setLoading(false); 
  };

  // Handler for closing modal
  const closeModal = () => {
    setShowEditModal(false);
    setEditTransaction(null);
  };

  // Handler for Delete button (opens confirmation modal)
  const handleDeleteClick = (transaction) => {
    setTransactionToDelete(transaction);
    setShowDeleteModal(true);
  };

  // Handler for confirming delete
  const handleConfirmDelete = async () => {
    if (!transactionToDelete || !transactionToDelete.id) return;

    const endpoint =
      transactionToDelete.type === "revenue"
        ? `${BASE_URL}api/revenues/${transactionToDelete.id}/`
        : `${BASE_URL}api/expenditures/${transactionToDelete.id}/`;

    const token = localStorage.getItem("token");
    try {
      setLoading(true); 
      const response = await axios.patch(
        endpoint,
        { deleted: true },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.status === 200 || response.status === 204) {
        setSuccessMessage("Transaction deleted successfully!");
        setTimeout(() => setSuccessMessage(""), 3000);
        // Refresh data after deletion
        await fetchFinancialReport();
      } else {
        setErrorMessage("Delete failed. Server responded with status: " + response.status);
        setTimeout(() => setErrorMessage(""), 3000);
      }
    } catch (err) {
      setErrorMessage("Failed to delete transaction.");
      setTimeout(() => setErrorMessage(""), 3000);
    }
    setShowDeleteModal(false);
    setTransactionToDelete(null);
    setLoading(false);
  };

  // Handler for cancelling delete
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setTransactionToDelete(null);
  };

  return (
    <div className="bg-white bg-opacity-50 rounded-xl shadow-lg p-4 sm:p-6 m-2 sm:m-4 w-full sm:w-[90%]">
      {/* Success Popup */}
      {successMessage && (
        <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-green-600 text-white px-6 py-3 rounded shadow-lg font-serif text-sm">
          {successMessage}
        </div>
      )}
      {/* Error Popup */}
      {errorMessage && (
        <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-red-600 text-white px-6 py-3 rounded shadow-lg font-serif text-sm">
          {errorMessage}
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        <div className="bg-white rounded-lg p-4 shadow">
          <p className="text-center text-xs uppercase font-semibold text-gray-600">
            REVENUE
          </p>
          <p className="text-center text-lg sm:text-xl font-bold text-orange-600">
            KSH {parseFloat(totalRevenue).toLocaleString()}
          </p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow">
          <p className="text-center text-xs uppercase font-semibold text-gray-600">
            EXPENDITURE
          </p>
          <p className="text-center text-lg sm:text-xl font-bold text-orange-600">
            KSH {parseFloat(totalExpenditure).toLocaleString()}
          </p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow">
          <p className="text-center text-xs uppercase font-semibold text-gray-600">
            PROFIT
          </p>
          <p className="text-center text-lg sm:text-xl font-bold text-orange-600">
            KSH {parseFloat(totalProfit).toLocaleString()}
          </p>
        </div>
      </div>

      {/* Filters Row */}
      <div className="bg-white rounded-lg p-4 py-2 shadow mb-4 overflow-x-auto">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <span className="text-xs whitespace-nowrap">DATE:</span>
                <input
                  type="date"
                  value={tempStartDate}
                  onChange={(e) => setTempStartDate(e.target.value)}
                  className="text-xs bg-gray-200 border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-orange-500 w-full sm:w-auto"
                />
                <span className="text-xs whitespace-nowrap">to</span>
                <input
                  type="date"
                  value={tempEndDate}
                  onChange={(e) => setTempEndDate(e.target.value)}
                  className="text-xs bg-gray-200 border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-orange-500 w-full sm:w-auto"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 sm:flex sm:flex-row gap-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1">
                <span className="text-xs whitespace-nowrap">CATEGORY</span>
                <select
                  value={tempSelectedCategory}
                  onChange={(e) => setTempSelectedCategory(e.target.value)}
                  className="text-xs bg-gray-200 border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-orange-500 w-full sm:w-auto"
                >
                  <option value="">All</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1">
                <span className="text-xs whitespace-nowrap">TYPE</span>
                <select
                  value={tempSelectedType}
                  onChange={(e) => setTempSelectedType(e.target.value)}
                  className="text-xs bg-gray-200 border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-orange-500 w-full sm:w-auto"
                >
                  <option value="">All</option>
                  <option value="revenue">Revenue</option>
                  <option value="expenditure">Expenditure</option>
                </select>
              </div>
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1">
                <span className="text-xs whitespace-nowrap">VEHICLE</span>
                <select
                  value={tempSelectedVehicle}
                  onChange={(e) => setTempSelectedVehicle(e.target.value)}
                  className="text-xs bg-gray-200 border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-orange-500 w-full sm:w-auto"
                >
                  <option value="">All</option>
                  {vehicles.map((vehicle) => (
                    <option key={vehicle.id} value={vehicle.registration_number}>
                      {vehicle.registration_number}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Search and Clear Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 justify-start">
            <button
              onClick={handleSearch}
              className="bg-orange-600 text-white px-4 py-2 rounded text-xs font-semibold hover:bg-orange-700 transition-colors duration-200 flex items-center gap-2"
            >
              🔍 Search
            </button>
            <button
              onClick={handleClearFilters}
              className="bg-gray-500 text-white px-4 py-2 rounded text-xs font-semibold hover:bg-gray-600 transition-colors duration-200 flex items-center gap-2"
            >
              🗑️ Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Chart and Table Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="bg-white rounded-lg p-4 shadow">
          <div className="flex justify-between items-center mb-4">
            <p className="text-xs uppercase font-semibold text-gray-600">
              REVENUE & EXPENDITURE
            </p>
          </div>
          <div className="h-64 sm:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barGraphData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="category"
                  tick={{ fontSize: 12 }}
                  height={60}
                  interval={0}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip />
                <Bar dataKey="amount" fill="#3887d6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow">
          <div className="flex justify-between items-center mb-4">
            <p className="text-xs uppercase font-semibold text-gray-600">
              TRANSACTIONS
            </p>
            <p className="text-xs text-gray-500 italic">
              (Latest reports first)
            </p>
          </div>
          {filteredData.length ? (
            <>
              <div className="overflow-x-auto -mx-4 sm:mx-0">
                <table className="min-w-full whitespace-nowrap">
                  <thead>
                    <tr>
                      <th className="bg-orange-600 text-white text-xs p-2 sticky left-0">DATE ↓</th>
                      <th className="bg-orange-600 text-white text-xs p-2">AMOUNT</th>
                      <th className="bg-orange-600 text-white text-xs p-2">TYPE</th>
                      <th className="bg-orange-600 text-white text-xs p-2">VEHICLE</th>
                      <th className="bg-orange-600 text-white text-xs p-2">DESCRIPTION</th>
                      <th className="bg-orange-600 text-white text-xs p-2">ACTIONS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredData
                      .map((row, index) => (
                        <tr
                          key={index}
                          className={index % 2 === 0 ? "bg-gray-100" : "bg-white"}
                        >
                          <td className="text-xs p-2 border sticky left-0 bg-inherit">{row.date}</td>
                          <td className="text-xs p-2 border">KSH {parseFloat(row.amount).toLocaleString()}</td>
                          <td className="text-xs text-left p-2 border capitalize">
                            {row.type}
                            {row.type === 'expenditure' && row.category && ` (${row.category})`}
                          </td>
                          <td className="text-xs p-2 border">{row.car || 'N/A'}</td>
                          <td className="text-xs text-left p-2 border overflow-hidden text-ellipsis" title={row.description}>
                            {row.description ? (row.description.length > 50 ? `${row.description.substring(0, 50)}...` : row.description) : 'N/A'}
                          </td>
                          <td className="text-xs p-2 border">
                            <button
                              className="text-blue-600 hover:underline mr-2"
                              onClick={() => handleEdit(row)}
                            >
                             ✏️Edit
                            </button>
                            <button
                              className="text-red-600 hover:underline"
                              onClick={() => handleDeleteClick(row)}
                            >
                              🗑️Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination Controls */}
              <div className="mt-4 flex justify-between items-center">
                <div className="text-xs text-gray-500">
                  Showing {Math.min((currentPage - 1) * pageSize + 1, totalCount)} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} entries
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className={`px-3 py-1 text-xs border rounded ${currentPage === 1 ? 'bg-gray-100 text-gray-400' : 'bg-white text-gray-700 hover:bg-orange-100'}`}
                  >
                    Previous
                  </button>

                  {/* Page numbers */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page =>
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    )
                    .map((page, index, array) => (
                      <React.Fragment key={page}>
                        {index > 0 && array[index - 1] !== page - 1 && (
                          <span className="px-2 py-1 text-xs text-gray-500">...</span>
                        )}
                        <button
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-1 text-xs border rounded ${currentPage === page ? 'bg-orange-600 text-white' : 'bg-white text-gray-700 hover:bg-orange-100'}`}
                        >
                          {page}
                        </button>
                      </React.Fragment>
                    ))}

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage >= totalPages}
                    className={`px-3 py-1 text-xs border rounded ${currentPage >= totalPages ? 'bg-gray-100 text-gray-400' : 'bg-white text-gray-700 hover:bg-orange-100'}`}
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex justify-center items-center h-40">
              <p className="text-lg text-gray-500">
                No transactions found based on your current data
              </p>
            </div>
          )}

          {/* Edit Modal */}
          {showEditModal && editTransaction && (
            <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-40">
              <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md relative">
                <button
                  className="absolute top-2 right-2 text-gray-500 hover:text-gray-800"
                  onClick={closeModal}
                >
                  &times;
                </button>
                <h2 className="text-lg font-serif font-bold mb-4">Edit Transaction</h2>
                <form
                  onSubmit={e => {
                    e.preventDefault();
                    //console.log("Submitting edit form:", editForm); // Log form data before submission
                    handleSaveEdit();
                  }}
                  className="font-serif"
                >
                  <div className="mb-2">
                    <label className="block text-xs font-semibold font-serif">Date:</label>
                    <input
                      type="date"
                      name="date"
                      value={editForm.date}
                      onChange={handleEditFormChange}
                      className="w-full border rounded px-2 py-1 text-xs font-serif"
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-xs font-semibold font-serif">Amount:</label>
                    <input
                      type="number"
                      name="amount"
                      value={editForm.amount}
                      onChange={handleEditFormChange}
                      className="w-full border rounded px-2 py-1 text-xs font-serif"
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-xs font-semibold font-serif">Type:</label>
                    <select
                      name="type"
                      value={editForm.type}
                      disabled 
                      className="w-full border rounded px-2 py-1 text-xs font-serif bg-gray-100 cursor-not-allowed"
                    >
                      <option value="revenue">Revenue</option>
                      <option value="expenditure">Expenditure</option>
                    </select>
                  </div>
                  {/* Only show category if expenditure */}
                  {editForm.type === "expenditure" && (
                    <div className="mb-2">
                      <label className="block text-xs font-semibold font-serif">Category:</label>
                      <select
                        name="category"
                        value={editForm.category || ""}
                        onChange={handleEditFormChange}
                        className="w-full border rounded px-2 py-1 text-xs font-serif"
                      >
                        <option value="fuel">Fuel</option>
                        <option value="insurance">Insurance</option>
                        <option value="maintenance">Maintenance and Repairs</option>
                        <option value="tires">Tires</option>
                        <option value="registration">Registration and Licensing</option>
                        <option value="loan">Loan Interest</option>
                        <option value="carwash">Car Wash</option>
                        <option value="parking">Parking Fees</option>
                        <option value="emergency">Emergency Services</option>
                        <option value="others">Others</option>
                      </select>
                    </div>
                  )}
                  <div className="mb-2">
                    <label className="block text-xs font-semibold font-serif">Vehicle:</label>
                    <select
                      name="car"
                      value={editForm.car}
                      onChange={handleEditFormChange}
                      disabled
                      className="w-full border rounded px-2 py-1 text-xs font-serif"
                    >
                      <option value="">Select Vehicle</option>
                      {vehicles.map(vehicle => (
                        <option key={vehicle.id} value={vehicle.registration_number}>
                          {vehicle.registration_number}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="mb-2">
                    <label className="block text-xs font-semibold font-serif">Description:</label>
                    <textarea
                      name="description"
                      value={editForm.description}
                      onChange={handleEditFormChange}
                      className="w-full border rounded px-2 py-1 text-xs font-serif"
                    />
                  </div>
                  <div className="mt-4 flex justify-center gap-2">
                    <button
                      type="button"
                      className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 font-serif"
                      onClick={closeModal}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 font-serif"
                    >
                      Save
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {showDeleteModal && (
            <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-40">
              <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-sm relative font-serif">
                <button
                  className="absolute top-2 right-2 text-gray-500 hover:text-gray-800"
                  onClick={handleCancelDelete}
                >
                  &times;
                </button>
                <h2 className="text-lg font-bold mb-4 text-center">Confirm Delete</h2>
                <p className="mb-4 text-sm text-center">
                  Are you sure you want to delete this transaction?
                </p>
                <div className="flex justify-center gap-4">
                  <button
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
                    onClick={handleCancelDelete}
                  >
                    No
                  </button>
                  <button
                    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                    onClick={handleConfirmDelete}
                  >
                    Yes
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}