import { Dialog } from '@headlessui/react';
import { useState } from 'react';
import axios from 'axios';
import { BASE_URL } from '../../../../services/config';

const PostExpenditure = ({ isOpen, onClose, vehicleNumber, vehicleId }) => {
  const [formData, setFormData] = useState({
    category: '',
    date: '',
    unitPrice: '',
    quantity: '',
    description: ''  
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateTotal = () => {
    const total = Number(formData.unitPrice) * Number(formData.quantity);
    return isNaN(total) ? 0 : total;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        setError("Authentication token not found. Please log in again.");
        return;
      }

      const total = calculateTotal(); 
      const expenditureData = {
        vehicle: vehicleId,
        date: formData.date,
        item_name: formData.category,
        quantity: Number(formData.quantity),
        amount: total,
        description: formData.description  
      };

      const response = await axios.post(
        `${BASE_URL}api/expenditures/`,
        expenditureData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          }
        }
      );

      if (response.status === 201) {
        setSuccess(`You have submitted expenditure worth KSH ${total} for ${formData.category} successfully`);
        
        setTimeout(() => {
          setFormData({
            category: '',
            date: '',
            unitPrice: '',
            quantity: '1',
            description: ''
          });
          setSuccess(null);
        }, 4000);
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to submit expenditure');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
    {(error || success) && (
      <div className="fixed top-5 left-1/2 transform -translate-x-1/2 z-[999]">
        {error && (
          <div className="bg-red-700 border border-red-700 text-white px-4 py-2 rounded shadow-md mb-2 animate-fade-in-out">
            {error}
          </div>
        )}
        {success && (
          <div className="bg-green-700 border border-green-700 text-white px-4 py-2 rounded shadow-md mb-2 animate-fade-in-out">
            {success}
          </div>
        )}
      </div>
    )}

    <Dialog
      open={isOpen}
      onClose={onClose}
      className="relative z-[990]"
    >
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="relative mx-auto w-full max-w-lg rounded-lg bg-white p-8 shadow-xl">
          
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl font-bold leading-none focus:outline-none"
            aria-label="Close dialog"
          >
            &times;
          </button>

          <div className="text-center mb-8">
            <Dialog.Title className="text-2xl font-bold">
              POST EXPENDITURE
            </Dialog.Title>
            <div className="text-orange-600 font-semibold text-lg mt-2">
              {vehicleNumber}
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-bold text-gray-700">CATEGORY</label>
              <select
                name="category"
                value={formData.category}
                onChange={handleChange}
                className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-orange-500 focus:border-orange-500"
                required
              >
                <option value="">Select expenditure category</option>
                <option value="fuel">Fuel</option>
                <option value="insurance">Insurance</option>
                <option value="maintenance">Maintenance and Repairs</option>
                <option value="tires">Tires</option>
                <option value="registration">Registration and Licensing</option>
                <option value="loan">Loan Interest</option>
                <option value="carwash">Car Wash</option>
                <option value="parking">Parking Fees</option>
                <option value="emergency">Emergency Services</option>
                <option value="others">Others</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-bold text-gray-700">DATE</label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-orange-500 focus:border-orange-500"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-bold text-gray-700">UNIT PRICE:</label>
                <div className="flex items-center">
                  <span className="text-gray-500 mr-2">KSH</span>
                  <input
                    type="number"
                    name="unitPrice"
                    value={formData.unitPrice}
                    onChange={handleChange}
                    className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-orange-500 focus:border-orange-500"
                    required
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-bold text-gray-700">QUANTITY</label>
                <input
                  type="number"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleChange}
                  className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-orange-500 focus:border-orange-500"
                  required
                  step="any" 
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-bold text-gray-700">DESCRIPTION (Optional)</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="mt-1 w-full rounded-md border border-gray-300 p-2 focus:ring-orange-500 focus:border-orange-500 min-h-[80px]"
                placeholder="Enter any additional details about this expenditure..."
                rows="3"
              />
            </div>

            <div className="text-right text-sm font-bold">
              TOTAL EXPENDITURE: <span className="text-orange-600">KSH {calculateTotal()}</span>
            </div>

            <button
              type="submit"
              className="w-full bg-orange-600 text-white py-3 rounded-md hover:bg-orange-700 transition-colors font-medium disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'SUBMIT'}
            </button>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
    </>
  );
};

export default PostExpenditure;
