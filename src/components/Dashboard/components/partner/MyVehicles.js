import React, { useState, useEffect, useCallback } from "react";
import { Dialog } from "@headlessui/react";
import axios from "axios";
import { BASE_URL } from "../../../../services/config";
import PartnerVehicle from "./PartnerVehicle";
import VehicleDetailsDialog from "./VehicleDetailsDialog";
import PostExpenditure from './PostExpenditure';
import SetPayments from './SetPayments';
import SpinnerTwo from "../../../spinner/SpinnerTwo";

const MyVehicles = ({ partnerId, setShowPaymentPlans }) => {
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [showPopup, setShowPopup] = useState(false);
  const [popupMessage, setPopupMessage] = useState("");
  const [popupType, setPopupType] = useState("success");
  const [vehicleMakes, setVehicleMakes] = useState([]);
  const [vehicleModels, setVehicleModels] = useState([]);
  const [workAreas, setWorkAreas] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [vehicleToDeleteId, setVehicleToDeleteId] = useState(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedVehicleForDetails, setSelectedVehicleForDetails] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredVehicles, setFilteredVehicles] = useState([]);
  const [isSelectionDialogOpen, setIsSelectionDialogOpen] = useState(false);
  const [isExpenditureOpen, setIsExpenditureOpen] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [isSetPaymentsOpen, setIsSetPaymentsOpen] = useState(false);


  const [vehicleFormData, setVehicleFormData] = useState({
    registrationNumber: "",
    vehicleMake: "",
    vehicleModel: "",
    yearOfManufacture: "",
    workingArea: "",
    workingDays: "",
    leaseAgreement: null,
    logBook: null,
    vehiclePhoto: null,
    ntsaInspection: null,
    ntsaExpiryDate: "",
    insurance: null,
    insuranceExpiryDate: "",
  });

  useEffect(() => {
    const fetchMyVehicles = async () => {
      setLoading(true);
      setError(null);
      try {
        const token = localStorage.getItem("token");
        const response = await axios.get(`${BASE_URL}api/vehicles/`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        setVehicles(response.data || []);
      } catch (err) {
        console.error("Error fetching vehicles:", err);
        setError(err.message || "Failed to fetch vehicles");
        setVehicles([]);
      } finally {
        setLoading(false);
      }
    };
    if (partnerId) {
      fetchMyVehicles();
    }
  }, [partnerId]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [makesRes, modelsRes, areasRes] = await Promise.all([
          axios.get(`${BASE_URL}api/vehicle-makes/`),
          axios.get(`${BASE_URL}api/vehicle-models/`),
          axios.get(`${BASE_URL}api/work-areas/`),
        ]);
        setVehicleMakes(makesRes.data || []);
        setVehicleModels(modelsRes.data || []);
        setWorkAreas(areasRes.data || []);
      } catch (error) {
        console.error("Error fetching vehicle makes/models/areas:", error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (showPopup) {
      const timer = setTimeout(() => {
        setShowPopup(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [showPopup]);

  useEffect(() => {
    if (searchQuery) {
      const filtered = vehicles.filter(vehicle =>
        vehicle.registration_number.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredVehicles(filtered);
    } else {
      setFilteredVehicles(vehicles);
    }
  }, [searchQuery, vehicles]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setVehicleFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    if (validationErrors[name]) {
      setValidationErrors((prev) => ({
        ...prev,
        [name]: null,
      }));
    }

    if (name === 'vehicleMake') {
      setVehicleFormData(prev => ({
        ...prev,
        vehicleModel: '',
        [name]: value,
      }));
    }
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    const file = files[0];
    if (file) {
      setVehicleFormData((prev) => ({ ...prev, [name]: file }));
      if (validationErrors[name]) {
        setValidationErrors((prev) => ({
          ...prev,
          [name]: null,
        }));
      }
    }
  };

  const validateForm = () => {
    const errors = {};
    if (!vehicleFormData.registrationNumber.trim()) errors.registrationNumber = "Registration number is required";
    if (!vehicleFormData.vehicleMake) errors.vehicleMake = "Vehicle make is required";
    if (!vehicleFormData.vehicleModel) errors.vehicleModel = "Vehicle model is required";
    if (!vehicleFormData.yearOfManufacture) errors.yearOfManufacture = "Year of manufacture is required";
    if (!vehicleFormData.workingArea) errors.workingArea = "Preferred work area is required";
    if (!vehicleFormData.workingDays.trim()) errors.workingDays = "Working days are required";

    if (vehicleFormData.ntsaExpiryDate && isNaN(Date.parse(vehicleFormData.ntsaExpiryDate))) {
      errors.ntsaExpiryDate = "Invalid NTSA expiry date.";
    }
    if (vehicleFormData.insuranceExpiryDate && isNaN(Date.parse(vehicleFormData.insuranceExpiryDate))) {
      errors.insuranceExpiryDate = "Invalid insurance expiry date.";
    }

    if (!partnerId) {
      errors.partner = "Partner ID is missing. Please log out and log back in.";
      showErrorPopup("Partner ID is missing. Please log out and log back in.");
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const showErrorPopup = (message) => {
    setPopupType("error");
    setPopupMessage(message);
    setShowPopup(true);
  };

  const showSuccessPopup = (message) => {
    setPopupType("success");
    setPopupMessage(message);
    setShowPopup(true);
  };

  const formatErrorMessage = (errorData) => {
    if (typeof errorData === "string") {
      return errorData;
    }
    if (errorData.detail) {
      return errorData.detail;
    }
    if (errorData.non_field_errors) {
      if (Array.isArray(errorData.non_field_errors)) {
        return errorData.non_field_errors.join(", ");
      }
      return errorData.non_field_errors;
    }
    if (typeof errorData === "object") {
      const errorMessages = [];
      for (const [key, value] of Object.entries(errorData)) {
        const formattedKey = key.replace(/_/g, ' ');
        if (Array.isArray(value)) {
          errorMessages.push(`${formattedKey}: ${value.join(", ")}`);
        } else if (typeof value === "string") {
          errorMessages.push(`${formattedKey}: ${value}`);
        }
      }
      return errorMessages.join(" | ");
    }
    return "Unknown error occurred";
  };

  const handleVehicleSubmit = async (e) => {
    e.preventDefault();

    if (!partnerId) {
      showErrorPopup("Partner ID is missing. Please log out and log back in.");
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setShowPopup(false);

    const submissionData = new FormData();

    // First append the partner ID to ensure it's included
    submissionData.append("partner", partnerId.toString()); // Convert to string to ensure proper formatting

    // Then append all other form fields
    submissionData.append("registration_number", vehicleFormData.registrationNumber);
    submissionData.append("vehicle_make", vehicleFormData.vehicleMake);
    submissionData.append("vehicle_model", vehicleFormData.vehicleModel);
    submissionData.append("preferred_work_area", vehicleFormData.workingArea);
    submissionData.append("work_days", vehicleFormData.workingDays);
    submissionData.append("year_of_manufacture", vehicleFormData.yearOfManufacture);

    // Optional file uploads
    if (vehicleFormData.leaseAgreement) submissionData.append("lease_agreement", vehicleFormData.leaseAgreement);
    if (vehicleFormData.logBook) submissionData.append("logbook", vehicleFormData.logBook);
    if (vehicleFormData.vehiclePhoto) submissionData.append("vehicle_photo", vehicleFormData.vehiclePhoto);
    if (vehicleFormData.ntsaInspection) submissionData.append("ntsa_inspection_doc", vehicleFormData.ntsaInspection);
    if (vehicleFormData.insurance) submissionData.append("insurance_doc", vehicleFormData.insurance);
    if (vehicleFormData.ntsaExpiryDate) submissionData.append("ntsa_expiry_date", vehicleFormData.ntsaExpiryDate);
    if (vehicleFormData.insuranceExpiryDate) submissionData.append("insurance_expiry_date", vehicleFormData.insuranceExpiryDate);

    // Log the data being sent
    console.log("Partner ID being sent:", partnerId);
    console.log("Form Data contents:");
    for (let [key, value] of submissionData.entries()) {
      console.log(`${key}:`, value);
    }

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        showErrorPopup("Authentication token not found. Please log in again.");
        setIsSubmitting(false);
        return;
      }

      const response = await axios.post(
        `${BASE_URL}api/vehicles/`,
        submissionData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Accept: "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 201) {
        showSuccessPopup("Vehicle registered successfully!");

        setVehicles((prevVehicles) => {
          const newVehicleList = [...prevVehicles, response.data];
          return newVehicleList;
        });

        setIsOpen(false);

        setVehicleFormData({
          registrationNumber: "",
          vehicleMake: "",
          vehicleModel: "",
          yearOfManufacture: "",
          workingArea: "",
          workingDays: "",
          leaseAgreement: null,
          logBook: null,
          vehiclePhoto: null,
          ntsaInspection: null,
          ntsaExpiryDate: "",
          insurance: null,
          insuranceExpiryDate: "",
        });
        setValidationErrors({});
      } else {
        showErrorPopup(`Vehicle registered, but received unexpected status: ${response.status}`);
      }
    } catch (error) {
      let errorMsg = "Failed to register vehicle. Please try again.";

      if (error.response) {
        errorMsg = formatErrorMessage(error.response.data);

        if (typeof error.response.data === 'object' && error.response.data !== null) {
          const backendErrors = {};
          for (const [key, value] of Object.entries(error.response.data)) {
            const frontendKey = key === 'registration_number' ? 'registrationNumber' : key;
            if (vehicleFormData.hasOwnProperty(frontendKey) || frontendKey === 'partner') {
              backendErrors[frontendKey] = Array.isArray(value) ? value.join(', ') : value;
            }
          }
          setValidationErrors(prev => ({ ...prev, ...backendErrors }));
        }

      } else if (error.request) {
        errorMsg = "No response received from server. Check network connection.";
      } else {
        errorMsg = `An unexpected error occurred: ${error.message}`;
      }
      showErrorPopup(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  const requestRemoveConfirmation = (vehicleId) => {
    setVehicleToDeleteId(vehicleId);
    setIsConfirmOpen(true);
  };

  const closeConfirmDialog = () => {
    setIsConfirmOpen(false);
    setVehicleToDeleteId(null);
  };

  const handleRemoveVehicle = async () => {
    if (!vehicleToDeleteId) return;

    const vehicleId = vehicleToDeleteId;
    const vehicleToRemove = vehicles.find(v => v.id === vehicleId);
    const regNum = vehicleToRemove ? vehicleToRemove.registration_number : `ID ${vehicleId}`;

    closeConfirmDialog();

    const originalVehicles = [...vehicles];
    setVehicles(prevVehicles => prevVehicles.filter(v => v.id !== vehicleId));

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        showErrorPopup("Authentication token not found.");
        setVehicles(originalVehicles);
        return;
      }

      const response = await axios.patch(
        `${BASE_URL}api/vehicles/${vehicleId}/remove_vehicle/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.status === 204 || response.status === 200) {
        showSuccessPopup(`Vehicle ${regNum} removed successfully.`);
      } else {
        showErrorPopup(`Failed to remove vehicle ${regNum}. Unexpected status: ${response.status}`);
        setVehicles(originalVehicles);
      }

    } catch (error) {
      setVehicles(originalVehicles);

      let errorMsg = `Failed to remove vehicle ${regNum}. `;
      if (error.response) {
        if (error.response.status === 404) {
          errorMsg += "Vehicle not found on server.";
        } else if (error.response.status === 403) {
          errorMsg += "You do not have permission to remove this vehicle.";
        } else {
          errorMsg += formatErrorMessage(error.response.data || "Server error.");
        }
      } else if (error.request) {
        errorMsg += "No response from server.";
      } else {
        errorMsg += "An unexpected error occurred.";
      }
      showErrorPopup(errorMsg);
    }
  };

  const handleShowDetails = useCallback((vehicleId) => {
    const vehicleToShow = vehicles.find(v => v.id === vehicleId);
    if (vehicleToShow) {
      const make = vehicleMakes.find(m => m.id?.toString() === vehicleToShow.vehicle_make?.toString());
      const model = vehicleModels.find(md => md.id?.toString() === vehicleToShow.vehicle_model?.toString());

      const vehicleWithNames = {
        ...vehicleToShow,
        make_name: make ? make.name : (typeof vehicleToShow.vehicle_make === 'string' ? vehicleToShow.vehicle_make : "N/A"),
        model_name: model ? model.name : (typeof vehicleToShow.vehicle_model === 'string' ? vehicleToShow.vehicle_model : "N/A")
      };

      setSelectedVehicleForDetails(vehicleWithNames);
      setIsDetailsDialogOpen(true);
    }
  }, [vehicles, vehicleMakes, vehicleModels]);

  const handleUpdateVehicle = (updatedVehicle) => {
    setVehicles((prev) =>
      prev.map((v) => (v.id === updatedVehicle.id ? updatedVehicle : v))
    );
  };

  if (error && vehicles.length === 0) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="relative bg-white bg-opacity-60 rounded-xl mb-8 mt-2 shadow-lg py-4 h-[80vh] md:h-[60vh] w-[90%] flex flex-col items-start justify-start overflow-hidden custom-scrollbar">
      {showPopup && (
        <div
          className={`fixed top-10 left-1/2 transform -translate-x-1/2 ${popupType === "success" ? "bg-green-500" : "bg-red-500"
            } text-white p-4 rounded shadow-lg z-[9999] font-serif text-sm md:text-base max-w-xs sm:max-w-sm md:max-w-md`}
          style={{ boxShadow: "0 0 20px rgba(0, 0, 0, 0.3)" }}
        >
          {popupMessage}
        </div>
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6 w-full">
        <div className="w-full sm:w-auto">
          <h1 className="text-xl font-bold font-serif text-gray-900 ml-4">My Vehicles</h1>
          <p className="text-sm text-gray-500 mt-1 font-serif">
            Total Vehicles: {vehicles.length}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto ms-auto mr-6">
          <div className="relative mx-5 w-[80%] md:mx-0  sm:w-64">
            <input
              type="text"
              placeholder="Search by registration..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
            />
            <svg
              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          <button
            onClick={() => setIsSelectionDialogOpen(true)}
            className="mx-5 w-[80%] md:mx-0 sm:w-auto px-6 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 text-sm font-medium transition-colors"
          >
            Setup Vehicles
          </button>
        </div>
      </div>


      <div className="flex-grow w-full overflow-y-auto custom-scrollbar px-4">
        {loading && vehicles.length === 0 ? (
          <SpinnerTwo />
        ) : filteredVehicles.length === 0 ? (
          <p className="text-center font-serif font-bold text-3xl text-gray-800 py-6">You currently have no managed vehicles.</p>
        ) : (
          <>
            <div className="overflow-x-auto">
              {filteredVehicles.map((vehicle) => {
                const make = vehicleMakes.find(m => m.id?.toString() === vehicle.vehicle_make?.toString());
                const model = vehicleModels.find(md => md.id?.toString() === vehicle.vehicle_model?.toString());
                const makeName = make ? make.name : (typeof vehicle.vehicle_make === 'string' ? vehicle.vehicle_make : "N/A");
                const modelName = model ? model.name : (typeof vehicle.vehicle_model === 'string' ? vehicle.vehicle_model : "N/A");

                return (
                  <PartnerVehicle
                    key={vehicle.id}
                    vehicle={vehicle}
                    makeNameProp={makeName}
                    modelNameProp={modelName}
                    onRemoveRequest={requestRemoveConfirmation}
                    onShowDetails={handleShowDetails}
                  />
                );
              })}
            </div>
          </>
        )}
      </div>

      <Dialog
        open={isSelectionDialogOpen}
        onClose={() => setIsSelectionDialogOpen(false)}
        className="relative z-[990]"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto w-full max-w-lg rounded-2xl bg-white p-8 shadow-2xl relative">
            <Dialog.Title className="text-lg text-center mb-6 font-semibold">
              Select to add vehicle or set payments for your vehicles
            </Dialog.Title>

            <div className="flex flex-col gap-4">
              <button
                onClick={() => {
                  if (vehicles.length === 0) {
                    showErrorPopup("You don't have any vehicles registered yet.");
                    return;
                  }
                  if (!partnerId) {
                    showErrorPopup("Partner ID is missing. Please log out and log back in.");
                    return;
                  }
                  setIsSelectionDialogOpen(false);
                  setIsSetPaymentsOpen(true);
                  setSelectedVehicle({
                    registration_number: "All Vehicles",
                    id: null,
                    partnerId: partnerId
                  });
                }}
                className="w-full px-4 py-2  bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors text-base font-medium"
              >
                Set Default Payment Settings
              </button>

              <button
                onClick={() => {
                  // if (vehicles.length === 0) {
                  //   showErrorPopup("You don't have any vehicles registered yet.");
                  //   return;
                  // }
                  if (!partnerId) {
                    showErrorPopup("Partner ID is missing. Please log out and log back in.");
                    return;
                  }
                  setIsSelectionDialogOpen(false);
                  setIsOpen(true);
                }}
                className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors text-base font-medium"
              >
                Add Vehicle
              </button>
            </div>

            <button
              onClick={() => setIsSelectionDialogOpen(false)}
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-500"
            >
              <span className="text-2xl font-bold">×</span>
            </button>
          </Dialog.Panel>
        </div>
      </Dialog>

      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        className="relative z-[990]"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4 overflow-y-auto">
          <Dialog.Panel
            className="mx-auto w-full max-w-3xl rounded-lg bg-white p-4 sm:p-6 shadow-xl"
            style={{
              marginTop: window.innerWidth < 640 ? '10vh' : '10vh',
              maxHeight: window.innerWidth < 640 ? '85vh' : '80vh',
              overflowY: 'auto'
            }}
          >
            <div className="relative flex items-center justify-center mb-4">
              <Dialog.Title className="text-xl font-bold text-center">
                Partner Car Registration Form
              </Dialog.Title>
              <button
                onClick={() => setIsOpen(false)}
                className="absolute right-0 top-0 text-gray-500 hover:text-gray-700 p-2"
                aria-label="Close dialog"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleVehicleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="registrationNumber" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    REGISTRATION NUMBER <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="registrationNumber"
                    name="registrationNumber"
                    placeholder="e.g., KCP911D"
                    className={`w-full px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.registrationNumber ? "border-red-500" : "border-gray-300"
                      } rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm`}
                    value={vehicleFormData.registrationNumber}
                    onChange={handleInputChange}
                    required
                  />
                  {validationErrors.registrationNumber && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.registrationNumber}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="vehicleMake" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    VEHICLE MAKE <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="vehicleMake"
                    name="vehicleMake"
                    className={`w-full px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.vehicleMake ? "border-red-500" : "border-gray-300"
                      } rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm bg-white`}
                    value={vehicleFormData.vehicleMake}
                    onChange={(e) => {
                      const { value } = e.target;
                      setVehicleFormData((prev) => ({
                        ...prev,
                        vehicleMake: value,
                        vehicleModel: "",
                      }));
                    }}
                    required
                  >
                    <option value="">Select Make</option>
                    {vehicleMakes.map((make) => (
                      <option key={make.id} value={make.id}>
                        {make.name}
                      </option>
                    ))}
                  </select>
                  {validationErrors.vehicleMake && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.vehicleMake}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="vehicleModel" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    VEHICLE MODEL <span className="text-red-500">*</span>
                  </label>
                  <select
                    name="vehicleModel"
                    className={`w-full px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.vehicleModel ? "border-red-500" : "border-gray-300"
                      } rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm bg-white disabled:bg-gray-100`}
                    value={vehicleFormData.vehicleModel}
                    onChange={handleInputChange}
                    required
                    disabled={!vehicleFormData.vehicleMake}
                  >
                    <option value="">Select Model</option>
                    {vehicleModels
                      .filter(model => model.make?.id?.toString() === vehicleFormData.vehicleMake)
                      .map((model) => (
                        <option key={model.id} value={model.id}>
                          {model.name}
                        </option>
                      ))}
                  </select>
                  {validationErrors.vehicleModel && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.vehicleModel}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="workingArea" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    PREFERRED WORK AREA <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="workingArea"
                    name="workingArea"
                    className={`w-full px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.workingArea ? "border-red-500" : "border-gray-300"
                      } rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm bg-white`}
                    value={vehicleFormData.workingArea}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select Area</option>
                    {workAreas.map((area) => (
                      <option key={area.id} value={area.id}>
                        {area.name}
                      </option>
                    ))}
                  </select>
                  {validationErrors.workingArea && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.workingArea}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="workingDays" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    WORKING DAYS <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="workingDays"
                    name="workingDays"
                    placeholder="e.g., 6"
                    className={`w-full px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.workingDays ? "border-red-500" : "border-gray-300"
                      } rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm`}
                    value={vehicleFormData.workingDays}
                    onChange={handleInputChange}
                    required
                  />
                  {validationErrors.workingDays && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.workingDays}</p>
                  )}
                </div>
                <div>
                  <label htmlFor="yearOfManufacture" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    YEAR OF MANUFACTURE <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="yearOfManufacture"
                    name="yearOfManufacture"
                    value={vehicleFormData.yearOfManufacture}
                    onChange={handleInputChange}
                    className={`w-full px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.yearOfManufacture ? "border-red-500" : "border-gray-300"
                      } rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm`}
                    required
                  >
                    <option value="">-- Select Year --</option>
                    {Array.from({ length: new Date().getFullYear() - 1989 }, (_, i) => {
                      const year = new Date().getFullYear() - i;
                      return (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      );
                    })}
                  </select>
                  {validationErrors.yearOfManufacture && (
                    <p className="text-red-500 text-xs mt-1">{validationErrors.yearOfManufacture}</p>
                  )}
                </div>

              </div>

              <div className="pt-4 border-t mt-4">
                <h3 className="text-md font-semibold text-gray-700 mb-1">Optional Uploads</h3>
                <p className="text-xs sm:text-sm text-red-600 mt-1 mb-3">
                  Tips: Sharing these details will help drivers with taxi app registrations making the process smoother and more convenient.
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="leaseAgreement" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    LEASE AGREEMENT
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 truncate">
                      {vehicleFormData.leaseAgreement?.name || "No file chosen"}
                    </div>
                    <input
                      type="file"
                      id="leaseAgreement"
                      name="leaseAgreement"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <label
                      htmlFor="leaseAgreement"
                      className="px-3 py-2 bg-gray-600 text-white rounded-md text-sm hover:bg-gray-700 cursor-pointer whitespace-nowrap"
                    >
                      Choose
                    </label>
                  </div>
                  {validationErrors.leaseAgreement && <p className="text-red-500 text-xs mt-1">{validationErrors.leaseAgreement}</p>}
                </div>
                <div>
                  <label htmlFor="logBook" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    LOGBOOK
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 truncate">
                      {vehicleFormData.logBook?.name || "No file chosen"}
                    </div>
                    <input
                      type="file"
                      id="logBook"
                      name="logBook"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <label
                      htmlFor="logBook"
                      className="px-3 py-2 bg-gray-600 text-white rounded-md text-sm hover:bg-gray-700 cursor-pointer whitespace-nowrap"
                    >
                      Choose
                    </label>
                  </div>
                  {validationErrors.logBook && <p className="text-red-500 text-xs mt-1">{validationErrors.logBook}</p>}
                </div>
                <div>
                  <label htmlFor="vehiclePhoto" className="block text-xs sm:text-sm font-medium text-black mb-1">
                    VEHICLE PHOTO
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 truncate">
                      {vehicleFormData.vehiclePhoto?.name || "No file chosen"}
                    </div>
                    <input
                      type="file"
                      id="vehiclePhoto"
                      name="vehiclePhoto"
                      onChange={handleFileChange}
                      accept="image/jpeg,image/png,image/jpg,application/pdf"
                      className="hidden"
                    />
                    <label
                      htmlFor="vehiclePhoto"
                      className="px-3 py-2 bg-gray-600 text-white rounded-md text-sm hover:bg-gray-700 cursor-pointer whitespace-nowrap"
                    >
                      Choose
                    </label>
                  </div>
                  {validationErrors.vehiclePhoto && <p className="text-red-500 text-xs mt-1">{validationErrors.vehiclePhoto}</p>}
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                    NTSA INSPECTION
                  </label>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
                    <div className="w-full sm:w-1/2">
                      <div className="flex items-center gap-2">
                        <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 truncate">
                          {vehicleFormData.ntsaInspection?.name || "No file chosen"}
                        </div>
                        <input
                          type="file"
                          id="ntsaInspection"
                          name="ntsaInspection"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                        <label
                          htmlFor="ntsaInspection"
                          className="px-3 py-2 bg-gray-600 text-white rounded-md text-sm hover:bg-gray-700 cursor-pointer whitespace-nowrap"
                        >
                          Choose
                        </label>
                      </div>
                      {validationErrors.ntsaInspection && <p className="text-red-500 text-xs mt-1">{validationErrors.ntsaInspection}</p>}
                    </div>
                    <div className="w-full sm:w-1/2 flex items-center gap-2">
                      <label htmlFor="ntsaExpiryDate" className="text-xs sm:text-sm font-medium text-black whitespace-nowrap">Expiry Date:</label>
                      <input
                        type="date"
                        id="ntsaExpiryDate"
                        name="ntsaExpiryDate"
                        value={vehicleFormData.ntsaExpiryDate}
                        onChange={handleInputChange}
                        className={`flex-1 px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.ntsaExpiryDate ? "border-red-500" : "border-gray-300"} rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm`}
                      />
                    </div>
                  </div>
                  {validationErrors.ntsaExpiryDate && <p className="text-red-500 text-xs mt-1 hidden sm:block">{validationErrors.ntsaExpiryDate}</p>}
                  {validationErrors.ntsaExpiryDate && <p className="text-red-500 text-xs mt-1 sm:hidden">{validationErrors.ntsaExpiryDate}</p>}
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-black mb-1">
                    INSURANCE
                  </label>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
                    <div className="w-full sm:w-1/2">
                      <div className="flex items-center gap-2">
                        <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 truncate">
                          {vehicleFormData.insurance?.name || "No file chosen"}
                        </div>
                        <input
                          type="file"
                          id="insurance"
                          name="insurance"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                        <label
                          htmlFor="insurance"
                          className="px-3 py-2 bg-gray-600 text-white rounded-md text-sm hover:bg-gray-700 cursor-pointer whitespace-nowrap"
                        >
                          Choose
                        </label>
                      </div>
                      {validationErrors.insurance && <p className="text-red-500 text-xs mt-1">{validationErrors.insurance}</p>}
                    </div>
                    <div className="w-full sm:w-1/2 flex items-center gap-2">
                      <label htmlFor="insuranceExpiryDate" className="text-xs sm:text-sm font-medium text-black whitespace-nowrap">Expiry Date:</label>
                      <input
                        type="date"
                        id="insuranceExpiryDate"
                        name="insuranceExpiryDate"
                        value={vehicleFormData.insuranceExpiryDate}
                        onChange={handleInputChange}
                        className={`flex-1 px-2 sm:px-3 py-1 sm:py-2 border ${validationErrors.insuranceExpiryDate ? "border-red-500" : "border-gray-300"} rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm`}
                      />
                    </div>
                  </div>
                  {validationErrors.insuranceExpiryDate && <p className="text-red-500 text-xs mt-1 hidden sm:block">{validationErrors.insuranceExpiryDate}</p>}
                  {validationErrors.insuranceExpiryDate && <p className="text-red-500 text-xs mt-1 sm:hidden">{validationErrors.insuranceExpiryDate}</p>}
                </div>
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  className="w-full bg-orange-600 text-white py-2 sm:py-3 px-4 rounded-md hover:bg-orange-700 transition-colors text-sm sm:text-base disabled:opacity-50"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Registering Vehicle..." : "Register Vehicle"}
                </button>
              </div>
              {validationErrors.partner && (
                <p className="text-red-500 text-xs mt-2 text-center">{validationErrors.partner}</p>
              )}
            </form>
          </Dialog.Panel>
        </div>
      </Dialog>

      <Dialog
        open={isConfirmOpen}
        onClose={closeConfirmDialog}
        className="relative z-[995]"
      >
        <div className="fixed inset-0 bg-black/40" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto max-w-sm rounded-lg bg-white p-6 shadow-xl w-96 text-center">
            <p className="text-lg font-medium mb-4">
              Are you sure you want to remove vehicle{" "}
              <span className="text-orange-500 font-bold">
                {vehicles.find(v => v.id === vehicleToDeleteId)?.registration_number || `ID ${vehicleToDeleteId}`}
              </span>?
              This action cannot be undone.
            </p>
            <div className="flex justify-around">
              <button
                type="button"
                onClick={handleRemoveVehicle}
                className="bg-green-500 text-white font-bold py-2 px-6 rounded hover:bg-green-600 transition"
              >
                YES
              </button>
              <button
                type="button"
                onClick={closeConfirmDialog}
                className="bg-red-500 text-white font-bold py-2 px-6 rounded hover:bg-red-600 transition"
              >
                NO
              </button>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>

      <VehicleDetailsDialog
        isOpen={isDetailsDialogOpen}
        onClose={() => setIsDetailsDialogOpen(false)}
        vehicleData={selectedVehicleForDetails}
        setShowPaymentPlans={setShowPaymentPlans}
        onUpdateVehicle={handleUpdateVehicle}
      />

      <PostExpenditure
        isOpen={isExpenditureOpen}
        onClose={() => setIsExpenditureOpen(false)}
        vehicleNumber={selectedVehicle}
      />

      <SetPayments
        isOpen={isSetPaymentsOpen}
        onClose={() => setIsSetPaymentsOpen(false)}
        vehicleNumber="All Vehicles"
        vehicleId={null}
        isDefault={true}
        totalVehicles={vehicles.length}
        partnerId={partnerId}
      />
    </div>
  );
};

export default MyVehicles;