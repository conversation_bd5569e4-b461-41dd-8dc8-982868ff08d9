import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer
} from 'recharts';
import api from "../../../../services/api";
import axios from "axios";
import { BASE_URL } from "../../../../services/config";

const DashboardMain = () => {
  const [vehicleCount, setVehicleCount] = useState(0);
  const [expectedRevenue, setExpectedRevenue] = useState(0);
  const [monthlyProfitData, setMonthlyProfitData] = useState([]);
  const [actualRevenue, setActualRevenue] = useState(0);
  const [vehicleKeys, setVehicleKeys] = useState([]);

  const currentMonthLabel = new Date().toLocaleString('default', { month: 'long' });

  useEffect(() => {
    const fetchAllData = async () => {
      const count = await fetchVehicles();
      if (count > 0) {
        await fetchExpectedRevenue(count);
      }
      await fetchMonthlyProfit();
    };
    fetchAllData();
  }, []);

  const fetchVehicles = async () => {
    try {
      const response = await api.get('api/vehicles/');
      const vehicleArray = Array.isArray(response) ? response : response.data || [];
      const count = vehicleArray.length;
      setVehicleCount(count);
      return count;
    } catch (error) {
      console.error("Error fetching vehicles:", error);
      setVehicleCount(0);
      return 0;
    }
  };

  const fetchExpectedRevenue = async (currentVehicleCount) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${BASE_URL}api/payment-settings/`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const settings = Array.isArray(response.data) ? response.data[0] : response.data;
      const dailyAmount = parseFloat(settings.daily_amount || 0);
      const paymentDays = settings.payment_days?.split(',') || [];

      const dayMap = {
        Sunday: 0, Monday: 1, Tuesday: 2, Wednesday: 3,
        Thursday: 4, Friday: 5, Saturday: 6
      };

      const targetDays = paymentDays.map(day => dayMap[day.trim()]);
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      let matchingDays = 0;
      for (let day = 1; day <= daysInMonth; day++) {
        const currentDate = new Date(year, month, day);
        if (targetDays.includes(currentDate.getDay())) {
          matchingDays++;
        }
      }

      const expected = dailyAmount * matchingDays * currentVehicleCount;
      setExpectedRevenue(expected);
    } catch (error) {
      console.error("Error fetching expected revenue:", error);
      setExpectedRevenue(0);
    }
  };

  const fetchMonthlyProfit = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${BASE_URL}api/financial-report/`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const { detailed_report } = response.data;
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();

      let actualRevenue = 0;
      const profitMap = {};
      const vehicleSet = new Set();

      detailed_report.forEach(item => {
        const date = new Date(item.date);
        const month = date.getMonth();
        const year = date.getFullYear();
        const key = `${year}-${month}`;
        const vehicle = item.vehicle || "Unknown";

        if (!profitMap[key]) profitMap[key] = {};
        if (!profitMap[key][vehicle]) profitMap[key][vehicle] = 0;

        const amount = parseFloat(item.amount);

        if (item.type === "Revenue") {
          profitMap[key][vehicle] += amount;
          if (month === currentMonth && year === currentYear) actualRevenue += amount;
        } else if (item.type === "Expenditure") {
          profitMap[key][vehicle] -= amount;
        }

        vehicleSet.add(vehicle);
      });

      const lastSixMonths = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const key = `${date.getFullYear()}-${date.getMonth()}`;
        lastSixMonths.push({
          key,
          label: date.toLocaleString('default', { month: 'short', year: 'numeric' })
        });
      }

      const formatted = lastSixMonths.map(({ key, label }) => {
        const monthData = profitMap[key] || {};
        return {
          month: label,
          ...monthData
        };
      });

      setMonthlyProfitData(formatted);
      setActualRevenue(actualRevenue);
      setVehicleKeys(Array.from(vehicleSet));
    } catch (error) {
      console.error("Error fetching monthly profit:", error);
      setMonthlyProfitData([]);
      setActualRevenue(0);
    }
  };

  const paymentCompletionRate = expectedRevenue > 0
    ? `${Math.round((actualRevenue / expectedRevenue) * 100)}%`
    : '0%';

  const colors = [
    '#3887d6', '#82ca9d', '#ffc658', '#ff8042',
    '#8dd1e1', '#a4de6c', '#d0ed57', '#8884d8',
    '#d88884', '#84d8c6', '#c684d8', '#d6a534',
    '#53a5d6', '#f07c7c', '#f0bf7c'
  ];

  return (
    <div className="bg-white bg-opacity-50 rounded-xl shadow-lg p-4 sm:p-6 m-2 sm:m-4 w-full sm:w-[90%] font-serif">
      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg p-4 shadow">
          <p className="text-center text-xs uppercase font-semibold text-gray-600">TOTAL VEHICLES</p>
          <p className="text-center text-lg sm:text-xl font-bold text-orange-600">{vehicleCount}</p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow">
          <p className="text-center text-xs uppercase font-semibold text-gray-600">
            EXPECTED REVENUE <span className="text-[13px] text-gray-400">({currentMonthLabel})</span>
          </p>
          <p className="text-center text-lg sm:text-xl font-bold text-orange-600">
            KSH {expectedRevenue.toLocaleString()}
          </p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow">
          <p className="text-center text-xs uppercase font-semibold text-gray-600">
            ACTUAL REVENUE <span className="text-[13px] text-gray-400">({currentMonthLabel})</span>
          </p>
          <p className="text-center text-lg sm:text-xl font-bold text-orange-600">
            KSH {actualRevenue.toLocaleString()}
          </p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow">
          <p className="text-center text-xs uppercase font-semibold text-gray-600">
            PAYMENT COMPLETION RATE <span className="text-[13px] text-gray-400">({currentMonthLabel})</span>
          </p>
          <p className="text-center text-lg sm:text-xl font-bold text-orange-600">
            {paymentCompletionRate}
          </p>
        </div>
      </div>

      {/* Chart */}
      <div className="bg-white rounded-lg p-4 shadow">
        <p className="text-xs uppercase font-bold text-gray-600 mb-4">
          Monthly Profit per Vehicle (Last 6 Months)
        </p>
        <div className="h-64 sm:h-80 overflow-x-auto">
          <div className="min-w-[500px] h-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={monthlyProfitData}
                margin={{ top: 10, right: 30, left: 0, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip />
                {vehicleKeys.map((key, index) => (
                  <Bar
                    key={key}
                    dataKey={key}
                    stackId="a"
                    fill={colors[index % colors.length]}
                  />
                ))}
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardMain;
