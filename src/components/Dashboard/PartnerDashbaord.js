import React, { useEffect, useState } from "react";
import NavBarThree from "../navbar/NavBarThree";
import ProfileSection from "./components/partner/ProfileSection";
import Footer from "../footer/Footer";
import PostedJobs from "./components/partner/PostedJobs";
import api from "../../services/api";
import Spinner from "../spinner/Spinner";
import MyDrivers from "./components/partner/MyDrivers";
import Applicants from "./components/partner/Applicants";
import MyVehicles from "./components/partner/MyVehicles";
import Reports from "./components/partner/Reports";
import WelcomePopUp from "./components/partner/Subscription/WelcomePopUp";
import PaymentAndPlans from "./components/partner/Subscription/PaymentAndPlans";
import UsedFreeSub from "./components/partner/Subscription/UsedFreeSub";
import ExpiredSubscription from './components/partner/Subscription/ExpiredSubcription';
import SubscriptionExpiryAlert from './components/partner/Subscription/SubscriptionExpiryAlert';
import DashboardMain from "./components/partner/DashboardMain";

import { getMetaTitle } from "../../utils/getMetaTitle";
import { Helmet } from 'react-helmet'

function PartnerDashboard() {
  const [profile, setProfile] = useState({});
  const [workAreas, setWorkAreas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [jobData, setJobData] = useState([]);
  const [activeSection, setActiveSection] = useState("dashboard");
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedJobId, setSelectedJobId] = useState(null);
  const [closingJobId, setClosingJobId] = useState(null);
  const [showPopup, setShowPopup] = useState(false);
  const [vehicleMakes, setVehicleMakes] = useState([]);
  const [vehicleModels, setVehicleModels] = useState([]);
  const [jobApplicants, setJobApplicants] = useState([]);
  const [vehicleTypes, setVehicleTypes] = useState([]);
  const [removingApplicationId, setRemovingApplicationId] = useState(null);
  const [acceptingApplicationId, setAcceptingApplicationId] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [showWelcome, setShowWelcome] = useState(false);
  const [showPaymentPlans, setShowPaymentPlans] = useState(false);
  const [showUsedFreeSub, setShowUsedFreeSub] = useState(false); // Add this state
  const [showExpiredSub, setShowExpiredSub] = useState(false);
  const [expiredPlanType, setExpiredPlanType] = useState(null);


  // Callback to update profile state
  const handleProfileUpdate = (updatedProfile) => {
    setProfile(updatedProfile);
    // Optionally update localStorage to persist the updated profile
    localStorage.setItem("profile", JSON.stringify(updatedProfile));
  };

  // Handle section change and update browser history
  const handleSectionChange = (section) => {
    if (section !== activeSection) {
      setActiveSection(section);
      window.history.pushState({ section }, "", "/partnerdashboard");
    }
  };

  // Listen for back/forward button
  useEffect(() => {
    const handlePopstate = (event) => {
      const section = event.state?.section || "reports";
      setActiveSection(section);
    };

    window.addEventListener("popstate", handlePopstate);
    window.history.replaceState({ section: "reports" }, "", "/partnerdashboard");

    return () => window.removeEventListener("popstate", handlePopstate);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const cachedProfile = localStorage.getItem("profile");
        const cachedWorkAreas = localStorage.getItem("workAreas");
        const cachedJobs = localStorage.getItem("jobs");
        const cachedVehicleMakes = localStorage.getItem("vehicleMakes");
        const cachedVehicleModels = localStorage.getItem("vehicleModels");
        const cachedVehicleTypes = localStorage.getItem("vehicleTypes");

        if (cachedProfile && cachedWorkAreas && cachedJobs) {
          setProfile(JSON.parse(cachedProfile));
          setWorkAreas(JSON.parse(cachedWorkAreas));
          setJobData(JSON.parse(cachedJobs));
          setVehicleMakes(JSON.parse(cachedVehicleMakes));
          setVehicleModels(JSON.parse(cachedVehicleModels));
          setVehicleTypes(JSON.parse(cachedVehicleTypes));
        }

        const profileData = await api.get("/api/users/me/");
        const workAreasData = await api.get("/api/work-areas/");
        const jobData = await api.get("/api/jobs/");
        const vehicleModelsData = await api.get("/api/vehicle-models/");
        const vehicleMakesData = await api.get("/api/vehicle-makes/");
        const vehicleTypesData = await api.get("/api/vehicle-types/");

        localStorage.setItem("profile", JSON.stringify(profileData));
        localStorage.setItem("workAreas", JSON.stringify(workAreasData));
        localStorage.setItem("jobs", JSON.stringify(jobData));
        localStorage.setItem("vehicleMakes", JSON.stringify(vehicleModelsData));
        localStorage.setItem("vehicleModels", JSON.stringify(vehicleMakesData));
        localStorage.setItem("vehicleTypes", JSON.stringify(vehicleTypesData));

        setProfile(profileData);
        setWorkAreas(workAreasData);
        setJobData(jobData);
        setVehicleMakes(vehicleMakesData);
        setVehicleModels(vehicleModelsData);
        setVehicleTypes(vehicleTypesData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchJobApplicants = async () => {
      try {
        const response = await api.get("/api/applications/");
        const applicants = response;
        if (Array.isArray(applicants)) {
          setJobApplicants(applicants);
        } else {
          console.error("API Response is not an array:", applicants);
        }
      } catch (error) {
        console.error("Error fetching job applicants: ", error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobApplicants();
  }, []);

  const getWorkAreaName = (id) => {
    const workArea = workAreas.find((area) => area.id === id);
    return workArea ? workArea.name : "Unknown";
  };

  const getVehicleMakeName = (makeId) => {
    const make = vehicleMakes.find((item) => item.id === makeId);
    return make ? make.name : "Unknown Make";
  };

  const getVehicleModelName = (modelId) => {
    const model = vehicleModels.find((item) => item.id === modelId);
    return model ? model.name : "Unknown Model";
  };

  const getVehicleTypeName = (id) => {
    const vehicleType = vehicleTypes.find((type) => type.id === id);
    return vehicleType ? vehicleType.name : "Unknown";
  };

  const handleRemove = async (applicationId) => {
    setRemovingApplicationId(applicationId);
    try {
      await api.post(`/api/applications/${applicationId}/remove_by_partner/`);
      const updatedApplicants = jobApplicants.map((applicant) =>
        applicant.id === applicationId
          ? { ...applicant, status: "Rejected" }
          : applicant
      );
      setJobApplicants(updatedApplicants);
      const response = await api.get("/api/applications/");
      setJobApplicants(response);
    } catch (error) {
      console.error("Error removing application:", error);
    } finally {
      setRemovingApplicationId(null);
    }
  };

  useEffect(() => {
    if (showPopup) {
      const timer = setTimeout(() => {
        setShowPopup(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showPopup]);

  const handleAccept = async (applicationId) => {
    setAcceptingApplicationId(applicationId);
    try {
      const response = await api.post(
        `/api/applications/${applicationId}/hire_driver/`
      );
      if (response) {
        const updatedApplicants = jobApplicants.map((applicant) =>
          applicant.id === applicationId
            ? { ...applicant, status: "Accepted" }
            : applicant
        );
        setJobApplicants(updatedApplicants);
        const newResponse = await api.get("/api/applications/");
        setJobApplicants(newResponse);
        return { success: true, status: 200 };
      }
      return { success: false, status: 400 };
    } catch (error) {
      console.error("Error accepting application:", error);
      return { success: false, status: 400 };
    } finally {
      setAcceptingApplicationId(null);
    }
  };


  // Update the subscription status check in useEffect
  useEffect(() => {
    const checkSubscriptionStatus = async () => {
      if (!profile?.partner_details?.id) {
        return;
      }

      try {
        const token = localStorage.getItem('token');
        const response = await api.get("api/subscriptions/subscription-history/", {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        const latestSubscription = Array.isArray(response) ?
          response.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]
          : response;

        if (latestSubscription) {
          const hasSuccessfulPayment = latestSubscription.latest_payment?.status === 'success';
          const isPending = latestSubscription.status === 'pending';
          const isExpired = new Date(latestSubscription.expiry_date) < new Date();
          console.table({
            planType: latestSubscription.plan_type,
            status: latestSubscription.status,
            expiry: latestSubscription.expiry_date,
            isExpiredCalc: isExpired
          });


          // Handle pending payments first
          if (isPending && !hasSuccessfulPayment) {
            setShowWelcome(false);
            setShowUsedFreeSub(false);
            setShowExpiredSub(false);
            setShowPaymentPlans(true);
            return;
          }

          // Handle expired free plan first
          if (latestSubscription.plan_type === 'Free' && isExpired) {
            setShowUsedFreeSub(true);
            setShowWelcome(false);
            setShowExpiredSub(false);
            setShowPaymentPlans(false);
            return;
          }

          // Handle other expired plans
          if (isExpired) {
            setExpiredPlanType(latestSubscription.plan_type);
            setShowExpiredSub(true);
            setShowWelcome(false);
            setShowUsedFreeSub(false);
            setShowPaymentPlans(false);
            return;
          }

          // Handle active or cancelled but not expired plans
          if (latestSubscription.status === 'active' ||
            hasSuccessfulPayment ||
            latestSubscription.status === 'cancelled') {
            setShowWelcome(false);
            setShowUsedFreeSub(false);
            setShowExpiredSub(false);
            setShowPaymentPlans(false);
            return;
          }

          // Default case - show welcome
          setShowWelcome(true);
          setShowUsedFreeSub(false);
          setShowExpiredSub(false);
          setShowPaymentPlans(false);
        }

      } catch (error) {
        console.error("Error checking subscription status:", error);
        if (error.response?.status === 404) {
          setShowWelcome(true);
          setShowUsedFreeSub(false);
          setShowExpiredSub(false);
          setShowPaymentPlans(false);
        }
      }
    };

    checkSubscriptionStatus();
  }, [profile]);


  const handleCloseJobClick = (event, jobId) => {
    event.preventDefault();
    setSelectedJobId(jobId);
    setShowConfirmDialog(true);
  };

  const handleCloseJob = async () => {
    if (!selectedJobId) return;
    setClosingJobId(selectedJobId);
    setShowConfirmDialog(false);

    try {
      await api.post(`/api/jobs/${selectedJobId}/close_job/`);
      const updatedJobData = jobData.filter((job) => job.id !== selectedJobId);
      setJobData(updatedJobData);
      handleSectionChange("postedJobs");
      setShowPopup(true);
    } catch (error) {
      console.error("Error closing job:", error);
      alert("Failed to close job. Please try again.");
    } finally {
      setClosingJobId(null);
      setSelectedJobId(null);
    }
  };

  if (loading) {
    return (
      <div>
        <Spinner />
      </div>
    );
  }

  const pageTitle = getMetaTitle(activeSection);

  return (
    <div className="flex flex-col min-h-screen bg-cover max-w-full"
      style={{ backgroundImage: "url(/assets/background3.png)" }}
    >
      <NavBarThree setShowPaymentPlans={setShowPaymentPlans} />
      <SubscriptionExpiryAlert setShowPaymentPlans={setShowPaymentPlans} />
      <Helmet> <title>{pageTitle}</title></Helmet>

      {showPaymentPlans ? (
        <PaymentAndPlans onBack={() => setShowPaymentPlans(false)}
          partnerId={profile.partner_details?.id}
        />
      ) : (
        <>
          {showPopup && (
            <div className="fixed top-26 font-serif left-1/2 transform -translate-x-1/2 bg-green-500 text-white p-4 rounded shadow-md z-50">
              Job closed successfully
            </div>
          )}
          <div className="flex flex-col flex-grow w-full items-center justify-center">
            <div className="bg-white relative bg-opacity-50 rounded-xl mt-3 shadow-lg px-2  md:px-4 m-4 md:mx-8 w-[90%]">
              <div className="text-center absolute -top-4 left-1/2 transform -translate-x-1/2">
                <img
                  src="/assets/profile2.png"
                  alt="Welcome"
                  className="mx-auto mb-1 w-[24%] md:w-[8%] h-auto"
                />
              </div>
              <div className="mt-12 xl:mt-20">
                <p className="xl:text-xl md:text-sm text-xs">
                  <span className="font-semibold"></span>{" "}
                  {profile.role
                    ? profile.role.charAt(0).toUpperCase() + profile.role.slice(1)
                    : "Loading..."}
                </p>
                <h2 className="text-sm md:text-xl xl:text-2xl font-bold font-serif text-black mb-2">
                  {loading ? (
                    // <Spinner />
                    <div>Loading...</div>
                  ) : (
                    <div>
                      {profile.first_name
                        ? `${profile.first_name} ${profile.last_name}`
                        : "No Data Available"}
                    </div>
                  )}
                </h2>
              </div>
              {/* Toggle Buttons */}
              <div className="mt-3">
                {/* Mobile View */}
                <div className="flex flex-col md:hidden w-full">
                  <div className="flex justify-between gap-2 mb-2">
                    {/* <button
                      className={`flex-1 py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "dashboard" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                        }`}
                      onClick={() => handleSectionChange("dashboard")}
                    >
                      DASHBOARD
                    </button> */}
                    <button
                      className={`flex-1 py-2 px-2 text-xs font-bold font-serif rounded-t ${activeSection === "dashboard"
                        ? "bg-orange-700 text-white"
                        : "bg-gray-300 text-black"
                        }`}
                      onClick={() => setActiveSection("dashboard")}
                    >
                      DASHBOARD
                    </button>
                    <button
                      className={`flex-1 py-2 px-2 text-xs font-bold font-serif rounded-t ${activeSection === "reports"
                        ? "bg-orange-700 text-white"
                        : "bg-gray-300 text-black"
                        }`}
                      onClick={() => setActiveSection("reports")}
                    >
                      REPORTS
                    </button>
                    <button
                      className={`flex-1 py-2 px-2 text-xs font-bold font-serif rounded-t ${activeSection === "myVehicles"
                        ? "bg-orange-700 text-white"
                        : "bg-gray-300 text-black"
                        }`}
                      onClick={() => setActiveSection("myVehicles")}
                    >
                      MY VEHICLES
                    </button>
                  </div>

                  <div className="relative group w-full">
                    <button className="w-full py-2 px-3 text-sm font-bold font-serif bg-gray-300 text-black rounded-t flex items-center justify-center">
                      MORE OPTIONS
                      <svg
                        className="w-4 h-4 ml-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>
                    <div className="absolute hidden group-hover:block w-full bg-white rounded-b shadow-lg z-50">
                      <button
                        className={`w-full py-2 px-3 text-sm font-bold font-serif text-left hover:bg-gray-100 ${activeSection === "postedJobs"
                          ? "text-orange-700"
                          : "text-black"
                          }`}
                        onClick={() => setActiveSection("postedJobs")}
                      >
                        JOBS POSTED
                      </button>
                      <button
                        className={`w-full py-2 px-3 text-sm font-bold font-serif text-left hover:bg-gray-100 ${activeSection === "my_drivers"
                          ? "text-orange-700"
                          : "text-black"
                          }`}
                        onClick={() => setActiveSection("my_drivers")}
                      >
                        MY DRIVERS
                      </button>
                      <button
                        className={`w-full py-2 px-3 text-sm font-bold font-serif text-left hover:bg-gray-100 ${activeSection === "applicants"
                          ? "text-orange-700"
                          : "text-black"
                          }`}
                        onClick={() => setActiveSection("applicants")}
                      >
                        APPLICANTS
                      </button>
                      <button
                        className={`w-full py-2 px-3 text-sm font-bold font-serif text-left hover:bg-gray-100 ${activeSection === "profile" ? "text-orange-700" : "text-black"
                          }`}
                        onClick={() => handleSectionChange("profile")}
                      >
                        MY PROFILE
                      </button>
                    </div>
                  </div>
                </div>

                {/* Desktop View */}
                <div className="hidden md:flex md:space-x-8 items-center justify-center">
                  <button
                    className={`py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "dashboard" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                      }`}
                    onClick={() => handleSectionChange("dashboard")}
                  >
                    DASHBOARD
                  </button>
                  <button
                    className={`py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "reports" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                      }`}
                    onClick={() => handleSectionChange("reports")}
                  >
                    REPORTS
                  </button>
                  <button
                    className={`py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "myVehicles" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                      }`}
                    onClick={() => handleSectionChange("myVehicles")}
                  >
                    MY VEHICLES
                  </button>
                  <button
                    className={`py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "postedJobs" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                      }`}
                    onClick={() => handleSectionChange("postedJobs")}
                  >
                    JOBS POSTED
                  </button>
                  <button
                    className={`py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "applicants" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                      }`}
                    onClick={() => handleSectionChange("applicants")}
                  >
                    APPLICANTS
                  </button>
                  <button
                    className={`py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "my_drivers" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                      }`}
                    onClick={() => handleSectionChange("my_drivers")}
                  >
                    MY DRIVERS
                  </button>
                  <button
                    className={`py-2 md:px-4 text-sm xl:text-xl font-bold font-serif rounded-t ${activeSection === "profile" ? "bg-orange-700 text-white" : "bg-gray-300 text-black"
                      }`}
                    onClick={() => handleSectionChange("profile")}
                  >
                    MY PROFILE
                  </button>
                </div>
              </div>
            </div>

            {/* Conditional Rendering of Sections */}
            {activeSection === "profile" && (
              <ProfileSection
                profile={profile}
                getWorkAreaName={getWorkAreaName}
                onProfileUpdate={handleProfileUpdate}
              />
            )}

            {activeSection === "postedJobs" && (
              <PostedJobs
                jobData={jobData}
                loading={loading}
                getWorkAreaName={getWorkAreaName}
                getVehicleMakeName={getVehicleMakeName}
                getVehicleModelName={getVehicleModelName}
                closingJobId={closingJobId}
                handleCloseJob={handleCloseJob}
                handleCloseJobClick={handleCloseJobClick}
                showConfirmDialog={showConfirmDialog}
                setShowConfirmDialog={setShowConfirmDialog}
                showPopup={setShowPopup}
              // setShowPaymentPlans={setShowPaymentPlans} 
              />
            )}

            {activeSection === "my_drivers" && (
              <MyDrivers
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                partnerId={profile.partner_details.id}
              />
            )}

            {activeSection === "myVehicles" && (
              <MyVehicles
                partnerId={profile.partner_details.id}
                setShowPaymentPlans={setShowPaymentPlans}
              />
            )}

            {activeSection === "applicants" && (
              <Applicants
                jobApplicants={jobApplicants}
                loading={loading}
                getWorkAreaName={getWorkAreaName}
                getVehicleTypeName={getVehicleTypeName}
                AcceptingApplicationId={acceptingApplicationId}
                removingApplicationId={removingApplicationId}
                handleRemove={handleRemove}
                handleAccept={handleAccept}
              />
            )}
            {activeSection === "reports" && <Reports />}
            {activeSection === "dashboard" && (
              <DashboardMain partnerId={profile.partner_details?.id} />
            )
            }
          </div>
          <Footer />
          <WelcomePopUp
            isOpen={showWelcome}
            onClose={() => setShowWelcome(false)}
            setShowPaymentPlans={setShowPaymentPlans}
            partnerId={profile.partner_details?.id}
          />

          <UsedFreeSub
            isOpen={showUsedFreeSub}
            onClose={() => setShowUsedFreeSub(false)}
            setShowPaymentPlans={setShowPaymentPlans}
          />

          <ExpiredSubscription
            isOpen={showExpiredSub}

            onClose={() => setShowExpiredSub(false)}
            setShowPaymentPlans={setShowPaymentPlans}
            planType={expiredPlanType}
          />
        </>
      )}
    </div>
  );
}

export default PartnerDashboard;

